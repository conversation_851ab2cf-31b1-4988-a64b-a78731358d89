# Tunis Doyen Rotary Club Email Platform

A modern email platform built with React Email and <PERSON>send for the Tunis Doyen Rotary Club.

## Features

- Beautiful, responsive email templates for:
  - Welcome emails
  - Newsletters
  - Event invitations
- Preview server for testing emails
- Scripts for sending emails programmatically

## Prerequisites

- Node.js 16.8 or later
- A Resend account (for sending emails)

## Getting Started

1. Clone the repository:
   ```bash
   git clone <repository-url>
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   Create a `.env` file in the root directory with your Resend API key:
   ```env
   RESEND_API_KEY=your_resend_api_key_here
   ```

## Previewing Emails

To preview emails in your browser:
```bash
npm run dev
```

Visit `http://localhost:3000` to see the email previews.

## Sending Emails

### Using CLI Scripts

1. Send a welcome email:
   ```bash
   npm run send-welcome <EMAIL> "<PERSON> Doe"
   ```

2. Send a newsletter:
   ```bash
   npm run send-newsletter <EMAIL> "<PERSON>"
   ```

3. Send an event invitation:
   ```bash
   npm run send-invitation <EMAIL> "John Doe" "Monthly Meeting" "September 15, 2023"
   ```

### Programmatic Usage

You can also use the email components directly in your applications:

```tsx
import WelcomeEmail from './emails/welcome';
import NewsletterEmail from './emails/newsletter';
import EventInvitationEmail from './emails/event-invitation';

// Use with Resend
const { data, error } = await resend.emails.send({
  from: 'Tunis Doyen Rotary Club <<EMAIL>>',
  to: ['<EMAIL>'],
  subject: 'Welcome to Tunis Doyen Rotary Club!',
  react: WelcomeEmail({ name: 'John Doe' }),
});
```

## Customizing Templates

Email templates are located in the `emails` directory. You can customize them by modifying the React components:

- `emails/welcome.tsx` - Welcome email template
- `emails/newsletter.tsx` - Newsletter template
- `emails/event-invitation.tsx` - Event invitation template

## Exporting Email Templates

To export email templates as static HTML files:
```bash
npm run export
```

The exported files will be saved in the `out` directory.

## Deployment

To deploy this platform for production use:

1. Set the `RESEND_API_KEY` environment variable
2. Verify your sending domain in Resend
3. Update the `from` addresses in the scripts to use your verified domain

## Domain Verification

For production use, you need to verify your domain in Resend:
1. Go to https://resend.com/domains
2. Click "Add Domain"
3. Enter your domain name
4. Follow the DNS verification instructions
5. Update the `from` addresses in the scripts to use your verified domain

## Learn More

- [React Email Documentation](https://react.email)
- [Resend Documentation](https://resend.com/docs)

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.