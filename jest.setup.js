// Jest setup file for React Email testing
require('@testing-library/jest-dom');

// Polyfills for React Email
global.TextDecoder = require('util').TextDecoder;
global.TextEncoder = require('util').TextEncoder;

// Mock environment variables
process.env.RESEND_API_KEY = 'test-api-key';
process.env.VERCEL_URL = 'https://test.vercel.app';

// Mock Resend
jest.mock('resend', () => ({
  Resend: jest.fn().mockImplementation(() => ({
    emails: {
      send: jest.fn().mockResolvedValue({
        data: { id: 'test-email-id' },
        error: null,
      }),
    },
  })),
}));

// Mock dotenv
jest.mock('dotenv', () => ({
  config: jest.fn(),
}));

// Setup for React Email components
global.React = require('react');