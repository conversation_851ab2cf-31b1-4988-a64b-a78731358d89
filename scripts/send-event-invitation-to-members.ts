// Send event invitation to all Rotary club members

import { resend } from '../lib/resend';
import EventInvitationEmail from '../emails/event-invitation';
import { render } from '@react-email/render';

import { generateICS } from '../lib/calendar';
import fs from 'fs';
import path from 'path';

// Event configuration
const EVENT_CONFIG = {
  clubName: 'Tunis Doyen Rotary Club',
  eventName: 'Réunion Hebdomadaire',
  eventDate: 'September 3, 2025',
  eventTime: '12:30 - 15:00',
  eventLocation: 'Le Baroque-Restaurant 32 Rue Felicien Challaye Mutuelleville Mutuelleville, Tunis, 1082',
  eventDescription: 'Le Rotary Club Tunis Doyen vous invite à sa prochaine réunion hebdomadaire. C\'est une excellente occasion de nous retrouver, d\'échanger sur nos futurs projets communautaires et de renforcer les liens d\'amitié qui nous unissent. Votre présence est essentielle pour faire avancer nos initiatives et notre engagement.',
  rsvpLink: 'https://tunisdoyenrotary.org/rsvp',
  language: 'fr' as const,
};

// Read members from JSON file
const membersPath = path.join(__dirname, '..', 'members.json');
const members = JSON.parse(fs.readFileSync(membersPath, 'utf8'));



async function sendEventInvitation() {
  console.info('🎯 Starting event invitation sending to all members...');
  console.info(`📧 Event: ${EVENT_CONFIG.eventName}`);
  console.info(`📅 Date: ${EVENT_CONFIG.eventDate}`);
  console.info(`⏰ Time: ${EVENT_CONFIG.eventTime}`);
  console.info(`📍 Location: ${EVENT_CONFIG.eventLocation}`);
  console.info(`🔗 RSVP Link: ${EVENT_CONFIG.rsvpLink}`);
  console.info(`🌍 Language: ${EVENT_CONFIG.language}`);
  console.info(`👥 Total members: ${members.length}`);

  let successCount = 0;
  let errorCount = 0;
  const errors: Array<{ member: string; error: string }> = [];

  for (const member of members) {
    const fullName = `${member.firstName} ${member.lastName}`;

    // Skip members with invalid email
    if (!member.email || member.email === 'N/A') {
      console.info(`⏭️ Skipping ${fullName} - no valid email`);
      errorCount++;
      continue;
    }

    try {
      console.info(`\n📧 Sending to: ${fullName} <${member.email}>`);

      // Render the React component to HTML
      const html = await render(
        EventInvitationEmail({
          name: fullName,
          clubName: EVENT_CONFIG.clubName,
          eventName: EVENT_CONFIG.eventName,
          eventDate: EVENT_CONFIG.eventDate,
          eventTime: EVENT_CONFIG.eventTime,
          eventLocation: EVENT_CONFIG.eventLocation,
          eventDescription: EVENT_CONFIG.eventDescription,
          rsvpLink: EVENT_CONFIG.rsvpLink,
          language: EVENT_CONFIG.language,
        })
      );

      // Generate ICS attachment with proper times
      const [startTime, endTime] = EVENT_CONFIG.eventTime.split('-').map(time => time.trim());
      const eventStart = new Date(`${EVENT_CONFIG.eventDate} ${startTime}`);
      const eventEnd = new Date(`${EVENT_CONFIG.eventDate} ${endTime}`);

      const icsContent = generateICS({
        title: EVENT_CONFIG.eventName,
        description: EVENT_CONFIG.eventDescription,
        location: EVENT_CONFIG.eventLocation,
        start: eventStart,
        end: eventEnd,
      });

      const data = await resend.emails.send({
        from: 'Tunis Doyen Rotary Club <<EMAIL>>',
        to: [member.email],
        subject: `Vous êtes invité: ${EVENT_CONFIG.eventName}`,
        html: html,
        attachments: [
          {
            content: Buffer.from(icsContent, 'utf-8').toString('base64'),
            filename: `${EVENT_CONFIG.eventName.replace(/[^a-z0-9]/gi, '_')}.ics`,
          },
        ],
      });

      if (!data) {
        throw new Error('No data returned from email service');
      }

      console.info(`✅ Email sent successfully to ${member.firstName} ${member.lastName}`);
      successCount++;

      // Add delay to avoid rate limits (adjust as needed)
      await new Promise(resolve => setTimeout(resolve, 500)); // 500ms delay

    } catch (error) {
      process.stderr.write(`❌ Failed to send to ${member.firstName} ${member.lastName}: ${error}\n`);
      errorCount++;
      errors.push({ member: fullName, error: error instanceof Error ? error.message : 'Unknown error' });
    }
  }

  // Summary
  console.info('\n=== SENDING SUMMARY ===');
  console.info(`✅ Successful sends: ${successCount}`);
  console.info(`❌ Failed sends: ${errorCount}`);

  if (errors.length > 0) {
    console.info('\n📝 Error Details:');
    errors.forEach(({ member, error }) => {
      console.info(`- ${member}: ${error}`);
    });
  }

  console.info('\n🎉 Event invitation sending completed!');
}

async function main() {
  try {
    await sendEventInvitation();
    process.exit(0);
  } catch (error) {
    process.stderr.write(`💥 Script failed: ${error}\n`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { sendEventInvitation };
