import { config } from 'dotenv';
config(); // Load environment variables from .env

import { resend } from '../lib/resend';
import WelcomeEmail from '../emails/welcome';

async function sendWelcomeEmail() {
  const email = process.argv[2];
  const name = process.argv[3] || 'Member';
  
  if (!email) {
    process.stderr.write('Error: Please provide an email address\n');
    process.exit(1);
  }

  try {
    const data = await resend.emails.send({
      from: 'Tunis Doyen Rotary Club <<EMAIL>>',
      to: [email],
      subject: 'Welcome to Tunis Doyen Rotary Club!',
      react: WelcomeEmail({ name }),
    });

    console.info(`Email sent successfully: ${data}`);
  } catch (err) {
    process.stderr.write(`Error sending email: ${err}\n`);
    process.exit(1);
  }
}

sendWelcomeEmail();
