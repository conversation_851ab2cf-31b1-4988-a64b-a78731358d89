import { resend } from '../lib/resend';
import NewsletterEmail from '../emails/newsletter';

async function sendNewsletter() {
  const email = process.argv[2];
  const name = process.argv[3] || 'Member';
  
  if (!email) {
    process.stderr.write('Error: Please provide an email address\n');
    process.exit(1);
  }

  try {
    const { data, error } = await resend.emails.send({
      from: 'Tunis Doyen Rotary Club <<EMAIL>>',
      to: [email],
      subject: 'Latest Updates from Tunis Doyen Rotary Club',
      react: NewsletterEmail({ name }),
    });

    if (!data) {
      process.stderr.write(`Error sending email: ${error}\n`);
      process.exit(1);
    }

    console.info(`Email sent successfully: ${data}`);
  } catch (err) {
    process.stderr.write(`Error sending email: ${err}\n`);
    process.exit(1);
  }
}

sendNewsletter();
