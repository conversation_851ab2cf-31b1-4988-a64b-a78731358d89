import { jest } from '@jest/globals';

// Mock the resend module
jest.mock('../../lib/resend', () => ({
  resend: {
    emails: {
      send: jest.fn(),
    },
  },
}));

import { resend } from '../../lib/resend';

// Mock process.argv
const originalArgv = process.argv;
const mockExit = jest.spyOn(process, 'exit').mockImplementation(() => {
  throw new Error('process.exit called');
});
const mockConsoleError = jest.spyOn(console, 'error').mockImplementation(() => {});
const mockConsoleLog = jest.spyOn(console, 'log').mockImplementation(() => {});

describe('send-invitation script', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset modules to ensure fresh import
    jest.resetModules();
  });

  afterEach(() => {
    process.argv = originalArgv;
    mockExit.mockClear();
    mockConsoleError.mockClear();
    mockConsoleLog.mockClear();
  });

  it('should send invitation email successfully', async () => {
    // Mock successful email send
    const mockSend = resend.emails.send as jest.MockedFunction<any>;
    mockSend.mockResolvedValue({
      data: { id: 'test-email-id' },
      error: null,
    });

    // Set up command line arguments
    process.argv = [
      'node',
      'scripts/send-invitation.ts',
      '<EMAIL>',
      'John Doe',
      'Annual Meeting',
      '2024-01-15',
    ];

    // Import and run the script
    const { sendInvitation } = await import('../send-invitation');

    await expect(sendInvitation()).resolves.toBeUndefined();

    expect(mockSend).toHaveBeenCalledWith({
      from: 'Test Mail Platform <<EMAIL>>',
      to: ['<EMAIL>'],
      subject: 'You\'re Invited: Annual Meeting',
      html: expect.any(String), // HTML content from render
    });

    expect(mockConsoleLog).toHaveBeenCalledWith(
      'Email sent successfully!',
      { data: { id: 'test-email-id' }, error: null }
    );
  });

  it('should use default values when optional arguments are not provided', async () => {
    const mockSend = resend.emails.send as jest.MockedFunction<any>;
    mockSend.mockResolvedValue({
      data: { id: 'test-email-id' },
      error: null,
    });

    process.argv = ['node', 'scripts/send-invitation.ts', '<EMAIL>'];

    const { sendInvitation } = await import('../send-invitation');

    await expect(sendInvitation()).resolves.toBeUndefined();

    expect(mockSend).toHaveBeenCalledWith({
      from: 'Test Mail Platform <<EMAIL>>',
      to: ['<EMAIL>'],
      subject: 'You\'re Invited: Monthly Club Meeting',
      html: expect.any(String),
    });
  });

  it('should exit with error when email is not provided', async () => {
    process.argv = ['node', 'scripts/send-invitation.ts'];

    const { sendInvitation } = await import('../send-invitation');

    await expect(sendInvitation()).rejects.toThrow('process.exit called');

    expect(mockConsoleError).toHaveBeenCalledWith('Please provide an email address');
    expect(mockExit).toHaveBeenCalledWith(1);
  });

  it('should handle email send errors', async () => {
    const mockSend = resend.emails.send as jest.MockedFunction<any>;
    mockSend.mockRejectedValue(new Error('Send failed'));

    process.argv = [
      'node',
      'scripts/send-invitation.ts',
      '<EMAIL>',
      'John Doe',
    ];

    const { sendInvitation } = await import('../send-invitation');

    await expect(sendInvitation()).rejects.toThrow('process.exit called');

    expect(mockConsoleError).toHaveBeenCalledWith('Error sending email:', expect.any(Error));
    expect(mockExit).toHaveBeenCalledWith(1);
  });

  it('should handle Resend API errors', async () => {
    const mockSend = resend.emails.send as jest.MockedFunction<any>;
    mockSend.mockResolvedValue({
      data: null,
      error: { message: 'API Error' },
    });

    process.argv = [
      'node',
      'scripts/send-invitation.ts',
      '<EMAIL>',
    ];

    const { sendInvitation } = await import('../send-invitation');

    await expect(sendInvitation()).rejects.toThrow('process.exit called');

    expect(mockConsoleError).toHaveBeenCalledWith('Error sending email: No data returned');
    expect(mockExit).toHaveBeenCalledWith(1);
  });
});