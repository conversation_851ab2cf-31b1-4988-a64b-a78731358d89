// Send invitation CLI tool

import yargs from "yargs";
import { hideBin } from "yargs/helpers";
import { resend } from '../lib/resend';
import EventInvitationEmail from '../emails/event-invitation';
import { render } from '@react-email/render';

import { generateICS } from '../lib/calendar';

// ---- CLI Definition ----
const argv = yargs(hideBin(process.argv))
  .scriptName("send-invitation")
  .usage("$0 <email> <name> <title> <date> [options]")
  .command(
    "$0 <email> <name> <title> <date>",
    "Send a meeting invitation",
    (yargs) =>
      yargs
        .positional("email", {
          describe: "Recipient email address",
          type: "string",
        })
        .positional("name", {
          describe: "Recipient name",
          type: "string",
        })
        .positional("title", {
          describe: "Event/meeting title",
          type: "string",
        })
        .positional("date", {
          describe: "Event date (e.g., 2025-09-04 or 'September 4, 2025')",
          type: "string",
        }),
  )
  .option("agenda", {
    alias: "a",
    type: "array",
    string: true,
    description: "Agenda items in 'time|description' format",
  })
  .option("time", {
    alias: "t",
    type: "string",
    description: "Event time (e.g., '6:00 PM - 8:00 PM')",
    default: "6:00 PM - 8:00 PM",
  })
  .option("location", {
    alias: "l",
    type: "string",
    description: "Event location",
    default: "Le Baroque-Restaurant, Tunis",
  })
  .option("description", {
    alias: "d",
    type: "string",
    description: "Event description",
    default: "Join us for our weekly meeting where we will discuss upcoming community projects and fellowship opportunities. Dinner will be provided.",
  })
  .option("rsvp", {
    alias: "r",
    type: "string",
    description: "RSVP link",
    default: "https://tunisdoyenrotary.org/rsvp",
  })
  .option("language", {
    alias: "lang",
    type: "string",
    choices: ['en', 'fr', 'ar'],
    description: "Language for the email (en: English, fr: French, ar: Arabic)",
    default: "en",
  })
  .example(
    `$0 <EMAIL> Monem "Weekly Club Meeting" "2025-09-04" --agenda "6:00 PM|Welcome Reception" --agenda "6:30 PM|President's Address"`,
    "Send an invitation with agenda"
  )
  .example(
    `$0 <EMAIL> John "Monthly Meeting" "2025-09-04" --language fr --agenda "18:00|Accueil"`,
    "Send French invitation with agenda"
  )
  .example(
    `$0 <EMAIL> Ahmed "جلسة شهرية" "2025-09-04" --language ar --agenda "18:00|الترحيب"`,
    "Send Arabic invitation with agenda"
  )
  .help()
  .parseSync();

// ---- Agenda parsing ----
const agenda = (argv.agenda || []).map((entry: string) => {
  const [time, item] = entry.split("|");
  return { time: time?.trim() || "", description: item?.trim() || "" };
}).filter(item => item.time || item.description);

// ---- Send invitation ----
async function sendInvitation() {
  try {

    console.log(`📧 Recipient: ${argv.name} <${argv.email}>`);
    console.log(`📧 Title: ${argv.title}`);
    console.log(`📧 Date: ${argv.date}`);
    console.log(`📧 Time: ${argv.time}`);
    console.log(`📧 Location: ${argv.location}`);
    console.log(`📧 Agenda items: ${agenda.length}`);
    console.log(`📧 Language: ${argv.language}`);

    // Language-specific subject translations
    const subjectTranslations = {
      en: `You're Invited: ${argv.title as string}`,
      fr: `Vous êtes invité: ${argv.title as string}`,
      ar: `أنت مدعو: ${argv.title as string}`,
    };

    const subject = subjectTranslations[argv.language as 'en' | 'fr' | 'ar'] || subjectTranslations.en;

    // Render the React component to HTML
    const html = await render(
      EventInvitationEmail({
        name: argv.name as string,
        eventName: argv.title as string,
        eventDate: argv.date as string,
        eventTime: argv.time as string,
        eventLocation: argv.location as string,
        eventDescription: argv.description as string,
        rsvpLink: argv.rsvp as string,
        agenda,
        language: argv.language as 'en' | 'fr' | 'ar'
      })
    );

    // Generate ICS attachment
    const eventStart = new Date((argv.date as string) + ' 18:00:00'); // Default start time 6 PM
    const eventEnd = new Date((argv.date as string) + ' 20:00:00'); // Default end time 8 PM
    const icsContent = generateICS({
      title: argv.title as string,
      description: agenda.length > 0 ? `${argv.description as string}\n\nAgenda:\n${agenda.map(item => `${item.time}: ${item.description}`).join('\n')}` : (argv.description as string),
      location: argv.location as string,
      start: eventStart,
      end: eventEnd,
    });

    const data = await resend.emails.send({
      from: 'Tunis Doyen Rotary Club <<EMAIL>>',
      to: [argv.email as string],
      subject: subject,
      html: html,
      attachments: [
        {
          content: Buffer.from(icsContent, 'utf-8').toString('base64'),
          filename: `${(argv.title as string).replace(/[^a-z0-9]/gi, '_')}.ics`,
        },
      ],
    });

    if (!data) {
      console.error('❌ Error sending email: No data returned');
      process.exit(1);
    }

    console.log('✅ Email sent successfully!', data);
  } catch (error) {
    console.error('❌ Error sending email:', error);
    process.exit(1);
  }
}

sendInvitation();
