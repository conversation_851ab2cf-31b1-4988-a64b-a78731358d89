// Send date correction and apology email to all Rotary club members

import { resend } from '../lib/resend';

import fs from 'fs';
import path from 'path';

// Event configuration
const EVENT_CONFIG = {
  clubName: 'Tunis Doyen Rotary Club',
  eventName: 'Réunion Hebdomadaire',
  eventDate: 'September 3, 2025',
  eventTime: '12:30 - 15:00',
  eventLocation: 'Le Baroque-Restaurant 32 Rue Felicien Challaye Mutuelleville Mutuelleville, Tunis, 1082',
};

// Apology email configuration
const APOLOGY_CONFIG = {
  subject: 'Désolé pour l\'erreur de date - Confirmation de la réunion Rotary',
  from: 'Tunis Doyen Rotary Club <<EMAIL>>',
  language: 'fr' as const,
};

// Read members from JSON file
const membersPath = path.join(__dirname, '..', 'members.json');
const members = JSON.parse(fs.readFileSync(membersPath, 'utf8'));



function generateApologyEmailHTML(memberName: string): string {
  return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            color: #333;
            max-width: 500px;
            margin: 0 auto;
            padding: 20px;
        }
    </style>
</head>
<body>
    <p>Cher ${memberName},</p>

    <p>Nous nous excusons sincèrement pour l'erreur de date dans notre précédente invitation.</p>

    <p>La réunion hebdomadaire aura lieu le <strong>3 septembre 2025</strong> à 12h30.</p>

    <p>C'est toujours au même endroit : ${EVENT_CONFIG.eventLocation.replace('Le Baroque-Restaurant 32 Rue Felicien Challaye Mutuelleville Mutuelleville, Tunis, 1082', 'Le Baroque-Restaurant, Tunis')}</p>

    <p>Cordialement,<br/>
    Le comité du ${EVENT_CONFIG.clubName}</p>
</body>
</html>
  `;
}

async function sendDateCorrectionEmail() {
  console.log('🎯 Starting date correction email sending to all members...');
  console.log(`📧 Subject: ${APOLOGY_CONFIG.subject}`);
  console.log(`📅 Correct Date: ${EVENT_CONFIG.eventDate}`);
  console.log(`🌍 Language: ${APOLOGY_CONFIG.language}`);
  console.log(`👥 Total members: ${members.length}`);

  let successCount = 0;
  let errorCount = 0;
  const errors: Array<{ member: string; error: string }> = [];

  // Preview email content for first member
  if (members.length > 0) {
    const firstMember = members[0];
    const previewHTML = generateApologyEmailHTML(`${firstMember.firstName} ${firstMember.lastName}`);
    console.log('\n=== EMAIL PREVIEW ===');
    console.log('Recipient:', `${firstMember.firstName} ${firstMember.lastName}`);
    console.log('\nHTML Content Preview:');
    console.log(previewHTML);
    console.log('\n====================================\n');

    // Ask user for confirmation
    console.log('⚠️  Please review the email content above.');
    console.log('🚀 Press "y" to send to all members, or "n" to cancel:');

    // For now, we'll show the preview but wait for user confirmation
    // In a real implementation, you might want to add readline for interactive confirmation

    // For demonstration, let's send to all members
    for (const member of members) {
      const fullName = `${member.firstName} ${member.lastName}`;

      // Skip members with invalid email
      if (!member.email || member.email === 'N/A') {
        console.log(`⏭️ Skipping ${fullName} - no valid email`);
        errorCount++;
        continue;
      }

      try {
        console.log(`\n📧 Sending to: ${fullName} <${member.email}>`);

        const html = generateApologyEmailHTML(fullName);

        const data = await resend.emails.send({
          from: APOLOGY_CONFIG.from,
          to: [member.email],
          subject: APOLOGY_CONFIG.subject,
          html: html,
        });

        if (!data) {
          throw new Error('No data returned from email service');
        }

        console.log(`✅ Apology email sent successfully to ${member.firstName} ${member.lastName}`);
        successCount++;

        // Add delay to avoid rate limits
        await new Promise(resolve => setTimeout(resolve, 500));

      } catch (error) {
        console.error(`❌ Failed to send to ${member.firstName} ${member.lastName}:`, error);
        errorCount++;
        errors.push({ member: fullName, error: error instanceof Error ? error.message : 'Unknown error' });
      }
    }
  }

  // Summary
  console.log('\n=== APOLOGY SENDING SUMMARY ===');
  console.log(`✅ Successful sends: ${successCount}`);
  console.log(`❌ Failed sends: ${errorCount}`);

  if (errors.length > 0) {
    console.log('\n� Error Details:');
    errors.forEach(({ member, error }) => {
      console.log(`- ${member}: ${error}`);
    });
  }

  console.log('\n🎉 Date correction email sending completed!');
}

async function main() {
  try {
    await sendDateCorrectionEmail();
    process.exit(0);
  } catch (error) {
    console.error('💥 Script failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { sendDateCorrectionEmail };
