// Generate contacts CSV for Resend import

import fs from 'fs';
import path from 'path';

// Read members from JSON file
const membersPath = path.join(__dirname, '..', 'members.json');
const members = JSON.parse(fs.readFileSync(membersPath, 'utf8'));



function generateContactsCSV(): string {
  // Header row for Resend CSV format
  const headers = [
    'email',
    'name',
    'updated_at',
    'tags'
  ];

  // Start with headers
  let csvContent = headers.join(',') + '\n';

  // Add each member as a row
  for (const member of members) {
    if (!member.email || member.email === 'N/A') {
      console.info(`⏭️ Skipping ${member.firstName} ${member.lastName} - no valid email`);
      continue;
    }

    const fullName = `${member.firstName} ${member.lastName}`.trim();
    const email = member.email.trim();

    // Current timestamp for updated_at
    const updatedAt = new Date().toISOString();

    // Tags for Rotarians and contact info
    const tags = [
      'Rotary_Member',
      member.memberNumber ? `${member.memberNumber}` : '',
      member.city ? `City_${member.city.replace(' ', '_')}` : '',
      'Tunis_Rotary_Club'
    ].filter(tag => tag.length > 0).join('; ');

    // Escape CSV fields that contain commas or quotes
    const escapeCSVField = (field: string): string => {
      if (field.includes(',') || field.includes('"') || field.includes('\n')) {
        return '"' + field.replace(/"/g, '""') + '"';
      }
      return field;
    };

    // Add this member's row
    const row = [
      escapeCSVField(email),
      escapeCSVField(fullName),
      escapeCSVField(updatedAt),
      escapeCSVField(tags)
    ].join(',');

    csvContent += row + '\n';
  }

  return csvContent;
}

function saveContactsCSV() {
  const csvContent = generateContactsCSV();
  const outputPath = path.join(__dirname, '..', 'rotary_members_contacts.csv');

  fs.writeFileSync(outputPath, csvContent, 'utf8');

  console.info('📄 CSV generated successfully!');
  console.info(`📂 File saved to: ${outputPath}`);
  console.info('\n📋 CSV Content Preview:');
  console.info('================');
  console.info(csvContent.split('\n').slice(0, 6).join('\n')); // Show first 5 lines
  console.info('================');
  console.info('... and ' + (csvContent.split('\n').length - 5) + ' more rows\n');

  // Show statistics
  const totalRows = csvContent.split('\n').length - 1; // Subtract header row
  console.info('📊 Statistics:');
  console.info(`👥 Total contacts: ${totalRows}`);
  console.info(`🔧 Headers: email, name, updated_at, tags`);
  console.info('🎯 Ready to import into Resend!');
}

async function main() {
  try {
    saveContactsCSV();
    console.info('\n✅ Contacts CSV generation completed!');
  } catch (error) {
    process.stderr.write(`💥 Script failed: ${error}\n`);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { generateContactsCSV, saveContactsCSV };
