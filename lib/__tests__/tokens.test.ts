import { TOKENS, TOKENS_JSON, injectCssVariables } from '../tokens';

describe('Design Tokens', () => {
  describe('TOKENS object', () => {
    it('should have all required color properties', () => {
      expect(TOKENS.color).toBeDefined();
      expect(TOKENS.color.brand).toBeDefined();
      expect(TOKENS.color.status).toBeDefined();
      expect(TOKENS.color.neutral).toBeDefined();
      expect(TOKENS.color.surface).toBeDefined();
    });

    it('should have correct brand colors', () => {
      expect(TOKENS.color.brand.royalBlue).toBe('#17458F');
      expect(TOKENS.color.brand.royalBlueDark).toBe('#123F7A');
      expect(TOKENS.color.brand.gold).toBe('#F7A81B');
    });

    it('should have correct status colors', () => {
      expect(TOKENS.color.status.success).toBe('#009739');
      expect(TOKENS.color.status.error).toBe('#D41367');
      expect(TOKENS.color.status.warning).toBe('#FF7600');
      expect(TOKENS.color.status.info).toBe('#00A2E0');
    });

    it('should have typography tokens', () => {
      expect(TOKENS.typography.family.primary).toBe(
        "\"Open Sans\", Arial, system-ui, sans-serif"
      );
      expect(TOKENS.typography.scale.h1).toBe('56px');
      expect(TOKENS.typography.weights.bold).toBe(700);
    });

    it('should have spacing tokens', () => {
      expect(TOKENS.spacing.base).toBe(8);
      expect(TOKENS.spacing.scale).toEqual([0, 4, 8, 12, 16, 24, 32, 40, 48, 64]);
    });

    it('should have radius tokens', () => {
      expect(TOKENS.radius.sm).toBe('6px');
      expect(TOKENS.radius.md).toBe('8px');
      expect(TOKENS.radius.lg).toBe('12px');
      expect(TOKENS.radius.pill).toBe('999px');
    });

    it('should have shadow tokens', () => {
      expect(TOKENS.shadow.sm).toBe('0 1px 2px rgba(16,24,40,0.05)');
      expect(TOKENS.shadow.md).toBe('0 6px 18px rgba(16,24,40,0.08)');
    });

    it('should have grid tokens', () => {
      expect(TOKENS.grid.columns).toBe(12);
      expect(TOKENS.grid.containerMax).toBe('1200px');
    });
  });

  describe('TOKENS_JSON', () => {
    it('should be a valid JSON string', () => {
      expect(typeof TOKENS_JSON).toBe('string');
      expect(() => JSON.parse(TOKENS_JSON)).not.toThrow();
    });

    it('should contain the same data as TOKENS object', () => {
      const parsed = JSON.parse(TOKENS_JSON);
      expect(parsed).toEqual(TOKENS);
    });
  });

  describe('injectCssVariables', () => {
    let mockDocument: any;

    beforeEach(() => {
      mockDocument = {
        documentElement: {
          style: {
            setProperty: jest.fn(),
          },
        },
      };
      global.document = mockDocument;
    });

    afterEach(() => {
      delete global.document;
    });

    it('should inject CSS variables with default prefix', () => {
      injectCssVariables();

      expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--ri-color-royalBlue',
        '#17458F'
      );
      expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--ri-color-gold',
        '#F7A81B'
      );
    });

    it('should inject CSS variables with custom prefix', () => {
      injectCssVariables('custom');

      expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--custom-color-royalBlue',
        '#17458F'
      );
    });

    it('should handle shadow tokens with quotes', () => {
      injectCssVariables();

      expect(mockDocument.documentElement.style.setProperty).toHaveBeenCalledWith(
        '--ri-shadow-sm',
        '0 1px 2px rgba(16,24,40,0.05)'
      );
    });

    it('should not throw when document is not available', () => {
      const originalDocument = global.document;
      (global as any).document = undefined;

      expect(() => injectCssVariables()).not.toThrow();

      // Restore
      global.document = originalDocument;
    });
  });
});