import { Resend } from 'resend';
import { resend } from '../resend';

jest.mock('resend');
jest.mock('dotenv', () => ({
  config: jest.fn(),
}));

describe('Resend Configuration', () => {
  const mockResend = Resend as jest.MockedClass<typeof Resend>;

  beforeEach(() => {
    jest.clearAllMocks();
    process.env.RESEND_API_KEY = 'test-api-key';
  });

  afterEach(() => {
    delete process.env.RESEND_API_KEY;
  });

  it('should initialize Resend with API key from environment', () => {
    expect(mockResend).toHaveBeenCalledWith('test-api-key');
    expect(resend).toBeDefined();
  });

  it('should throw error if RESEND_API_KEY is not set', () => {
    delete process.env.RESEND_API_KEY;

    // Re-import to trigger the error
    jest.resetModules();
    expect(() => {
      require('../resend');
    }).toThrow('RESEND_API_KEY environment variable is required');
  });

  it('should export resend instance', () => {
    expect(resend).toBeInstanceOf(Resend);
  });

  it('should have emails property', () => {
    expect(resend.emails).toBeDefined();
    expect(typeof resend.emails.send).toBe('function');
  });
});