// lib/calendar.ts

// Format UTC date to YYYYMMDDTHHmmssZ
const formatUtc = (date: Date) => {
  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, '0');
  const day = String(date.getUTCDate()).padStart(2, '0');
  const hours = String(date.getUTCHours()).padStart(2, '0');
  const minutes = String(date.getUTCMinutes()).padStart(2, '0');
  const seconds = String(date.getUTCSeconds()).padStart(2, '0');
  return `${year}${month}${day}T${hours}${minutes}${seconds}Z`;
};

export function generateGoogleCalendarUrl({
  title,
  description,
  location,
  start,
  end,
}: {
  title: string;
  description?: string;
  location?: string;
  start: Date;
  end: Date;
}) {
  const base = "https://calendar.google.com/calendar/render";
  const params = new URLSearchParams({
    action: "TEMPLATE",
    text: title,
    details: description || "",
    location: location || "",
    dates: `${formatUtc(start)}/${formatUtc(end)}`,
  });
  return `${base}?${params.toString()}`;
}

export function generateICS({
  title,
  description,
  location,
  start,
  end,
}: {
  title: string;
  description?: string;
  location?: string;
  start: Date;
  end: Date;
}) {
  return `BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Rotary Event//EN
BEGIN:VEVENT
UID:${Date.now()}@rotary
DTSTAMP:${formatUtc(new Date())}
DTSTART:${formatUtc(start)}
DTEND:${formatUtc(end)}
SUMMARY:${title}
DESCRIPTION:${description || ""}
LOCATION:${location || ""}
END:VEVENT
END:VCALENDAR`;
}
