// Design Tokens for Rotary UI Kit
// Extracted from doc/rotary_ui_kit_tokens_example_component.jsx

export interface ColorTokens {
  brand: {
    royalBlue: string;
    royalBlueDark: string;
    gold: string;
  };
  status: {
    success: string;
    error: string;
    warning: string;
    info: string;
  };
  neutral: {
    white: string;
    nearBlack: string;
    trueBlack: string;
    charcoal: string;
    slate: string;
    smoke: string;
    powderBlue: string;
  };
  surface: {
    overlay: string;
  };
}

export interface TypographyTokens {
  family: {
    primary: string;
  };
  link: {
    hoverColor: string;
    visitedStyle: string;
  };
  scale: {
    h1: string;
    h2: string;
    h3: string;
    body: string;
    small: string;
  };
  weights: {
    regular: number;
    medium: number;
    semibold: number;
    bold: number;
  };
}

export interface SpacingTokens {
  base: number;
  scale: number[];
}

export interface RadiusTokens {
  sm: string;
  md: string;
  lg: string;
  pill: string;
}

export interface ShadowTokens {
  sm: string;
  md: string;
}

export interface GridTokens {
  columns: number;
  containerMax: string;
}

export interface Tokens {
  color: ColorTokens;
  typography: TypographyTokens;
  spacing: SpacingTokens;
  radius: RadiusTokens;
  shadow: ShadowTokens;
  grid: GridTokens;
}

export const TOKENS: Tokens = {
  color: {
    brand: {
      royalBlue: "#17458F",
      royalBlueDark: "#123F7A",
      gold: "#F7A81B"
    },
    status: {
      success: "#009739",
      error: "#D41367",
      warning: "#FF7600",
      info: "#00A2E0"
    },
    neutral: {
      white: "#FFFFFF",
      nearBlack: "#111111",
      trueBlack: "#000000",
      charcoal: "#54565A",
      slate: "#657F99",
      smoke: "#B1B1B1",
      powderBlue: "#B9D9EB"
    },
    surface: {
      overlay: "rgba(23,69,143,0.6)"
    }
  },
  typography: {
    family: {
      primary: "\"Open Sans\", Arial, system-ui, sans-serif"
    },
    link: {
      hoverColor: "rgb(18,63,122)",
      visitedStyle: "inherit"
    },
    scale: {
      h1: "56px",
      h2: "40px",
      h3: "28px",
      body: "16px",
      small: "13px"
    },
    weights: {
      regular: 400,
      medium: 500,
      semibold: 600,
      bold: 700
    }
  },
  spacing: {
    base: 8,
    scale: [0, 4, 8, 12, 16, 24, 32, 40, 48, 64]
  },
  radius: {
    sm: "6px",
    md: "8px",
    lg: "12px",
    pill: "999px"
  },
  shadow: {
    sm: "0 1px 2px rgba(16,24,40,0.05)",
    md: "0 6px 18px rgba(16,24,40,0.08)"
  },
  grid: {
    columns: 12,
    containerMax: "1200px"
  }
};

// Optional: export JSON string for systems that expect a .json file.
export const TOKENS_JSON = JSON.stringify(TOKENS, null, 2);

// Optional: export helper function to inject CSS variables
export function injectCssVariables(prefix = '--ri') {
  if (typeof document === 'undefined') return;
  const root = document.documentElement;
  const set = (k: string, v: string) => root.style.setProperty(`${prefix}-${k}`, v);

  // colors
  Object.entries(TOKENS.color.brand).forEach(([k, v]) => set(`color-${k}`, v));
  Object.entries(TOKENS.color.status).forEach(([k, v]) => set(`color-${k}`, v));
  Object.entries(TOKENS.color.neutral).forEach(([k, v]) => set(`color-${k}`, v));
  Object.entries(TOKENS.color.surface).forEach(([k, v]) => set(`color-${k}`, v));
  // typography
  Object.entries(TOKENS.typography.link).forEach(([k, v]) => set(`typo-link-${k}`, v));
  // radius
  Object.entries(TOKENS.radius).forEach(([k, v]) => set(`radius-${k}`, v));
  // shadows
  Object.entries(TOKENS.shadow).forEach(([k, v]) => set(`shadow-${k}`, v.replace(/"/g, '\\"')));
}