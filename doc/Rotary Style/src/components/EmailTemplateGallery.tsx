import { RotaryEmailTemplate } from './RotaryEmailTemplate';
import { SectionHeader } from './SectionHeader';

export function EmailTemplateGallery() {
  const templates = [
    {
      id: 'webinar',
      title: 'Webinar Event Template',
      description: 'Perfect for promoting educational webinars and online events',
      props: {
        eventTitle: "Join Our Live Webinar: Digital Marketing Breakthroughs!",
        eventDate: "March 15th, 2024",
        eventTime: "2:00PM EST",
        eventDescription: "Discover innovative marketing strategies to revolutionize your brand's online presence.",
        logoText: "Rotary Club",
        headerSubtitle: "Learn cutting-edge strategies from industry experts!"
      }
    },
    {
      id: 'meeting',
      title: 'Club Meeting Template',
      description: 'Ideal for weekly club meetings and member communications',
      props: {
        eventTitle: "Weekly Club Meeting: Community Service Planning",
        eventDate: "Every Tuesday",
        eventTime: "7:00PM Local",
        eventDescription: "Join fellow Rotarians as we plan upcoming community service projects and discuss club business.",
        logoText: "Rotary Club Downtown",
        headerSubtitle: "Building stronger communities through service above self"
      }
    },
    {
      id: 'fundraiser',
      title: 'Fundraising Event Template',
      description: 'Great for charity events and fundraising campaigns',
      props: {
        eventTitle: "Annual Charity Gala: Making a Difference Together",
        eventDate: "April 20th, 2024",
        eventTime: "6:00PM",
        eventDescription: "Join us for an evening of giving back to our community through impactful charitable initiatives.",
        logoText: "Rotary Foundation",
        headerSubtitle: "Together, we can change lives and build a better world"
      }
    }
  ];

  return (
    <div className="space-y-12">
      <SectionHeader 
        title="Rotary Email Templates"
        subtitle="Professional email templates designed with the Rotary brand guidelines for various club communications"
      />
      
      {templates.map((template) => (
        <div key={template.id} className="space-y-6">
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="text-h3-desktop text-[#17458F] mb-2">{template.title}</h3>
            <p className="text-body-default text-gray-600 mb-4">{template.description}</p>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-body-default font-medium mb-2">Template Features:</h4>
              <ul className="text-body-small text-gray-600 space-y-1">
                <li>• Uses Rotary Royal Blue (#17458F) and Gold (#F7A81B) color scheme</li>
                <li>• Implements Rotary typography standards with Open Sans font</li>
                <li>• Responsive design that works across email clients</li>
                <li>• Professional speaker/member spotlight sections</li>
                <li>• Clear call-to-action buttons using Rotary button components</li>
                <li>• Social media integration in footer</li>
              </ul>
            </div>
          </div>
          
          {/* Email Template Preview */}
          <div className="bg-gray-100 p-8 rounded-lg">
            <div className="max-w-[600px] mx-auto shadow-lg">
              <RotaryEmailTemplate {...template.props} />
            </div>
          </div>
          
          {/* Usage Guidelines */}
          <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
            <h4 className="text-h3-mobile text-[#17458F] mb-3">Usage Guidelines</h4>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h5 className="text-body-default font-medium mb-2">Customization:</h5>
                <ul className="text-body-small text-gray-700 space-y-1">
                  <li>• Replace event details with your specific information</li>
                  <li>• Update speaker photos and bios as needed</li>
                  <li>• Customize the hero image to match your event</li>
                  <li>• Adjust club name and branding elements</li>
                </ul>
              </div>
              <div>
                <h5 className="text-body-default font-medium mb-2">Best Practices:</h5>
                <ul className="text-body-small text-gray-700 space-y-1">
                  <li>• Keep subject lines concise and engaging</li>
                  <li>• Test emails across different clients</li>
                  <li>• Include clear unsubscribe options</li>
                  <li>• Maintain consistent Rotary branding</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      ))}
      
      {/* Template Specifications */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-h3-desktop text-[#17458F] mb-4">Technical Specifications</h3>
        <div className="grid md:grid-cols-3 gap-6">
          <div>
            <h4 className="text-body-default font-medium mb-2">Dimensions</h4>
            <ul className="text-body-small text-gray-600 space-y-1">
              <li>• Width: 600px maximum</li>
              <li>• Responsive design</li>
              <li>• Mobile-optimized layout</li>
            </ul>
          </div>
          <div>
            <h4 className="text-body-default font-medium mb-2">Colors</h4>
            <ul className="text-body-small text-gray-600 space-y-1">
              <li>• Primary: Royal Blue (#17458F)</li>
              <li>• Secondary: Gold (#F7A81B)</li>
              <li>• Background: White (#FFFFFF)</li>
            </ul>
          </div>
          <div>
            <h4 className="text-body-default font-medium mb-2">Typography</h4>
            <ul className="text-body-small text-gray-600 space-y-1">
              <li>• Font: Open Sans</li>
              <li>• Fallback: Arial, sans-serif</li>
              <li>• Hierarchy: H1, H2, Body styles</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
