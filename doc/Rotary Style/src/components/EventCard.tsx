import { ReactNode } from 'react';

interface EventCardProps {
  title: string;
  date?: string;
  location?: string;
  description?: string;
  children?: ReactNode;
  className?: string;
}

export function EventCard({ 
  title, 
  date, 
  location, 
  description, 
  children, 
  className = '' 
}: EventCardProps) {
  return (
    <div className={`bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200 ${className}`}>
      <h3 className="text-h3-mobile text-gray-900 mb-2">{title}</h3>
      
      {date && (
        <p className="text-body-small text-gray-600 mb-1">
          <span className="font-medium">Date:</span> {date}
        </p>
      )}
      
      {location && (
        <p className="text-body-small text-gray-600 mb-2">
          <span className="font-medium">Location:</span> {location}
        </p>
      )}
      
      {description && (
        <p className="text-body-default text-gray-800 mb-3">{description}</p>
      )}
      
      {children}
    </div>
  );
}