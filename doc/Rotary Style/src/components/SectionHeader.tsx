interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  className?: string;
}

export function SectionHeader({ title, subtitle, className = '' }: SectionHeaderProps) {
  return (
    <div className={`mb-6 ${className}`}>
      <h2 className="text-h2-desktop text-gray-900 mb-2">{title}</h2>
      {subtitle && (
        <p className="text-body-small text-gray-600">{subtitle}</p>
      )}
    </div>
  );
}