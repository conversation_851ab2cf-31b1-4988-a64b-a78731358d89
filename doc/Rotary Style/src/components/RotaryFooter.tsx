import { ReactNode } from 'react';

interface RotaryFooterProps {
  children?: ReactNode;
  className?: string;
  textColor?: 'gold' | 'white';
}

export function RotaryFooter({ children, className = '', textColor = 'white' }: RotaryFooterProps) {
  const textColorClass = textColor === 'gold' ? 'text-[#F7A81B]' : 'text-white';
  
  return (
    <footer className={`bg-[#17458F] py-8 px-6 ${className}`}>
      <div className="max-w-6xl mx-auto">
        <div className={`${textColorClass} text-center`}>
          {children || (
            <div>
              <h3 className="text-h3-desktop mb-4">Rotary International</h3>
              <p className="text-body-default mb-2">Service Above Self</p>
              <p className="text-body-small">© 2024 Rotary International. All rights reserved.</p>
            </div>
          )}
        </div>
      </div>
    </footer>
  );
}