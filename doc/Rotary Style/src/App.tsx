import { ColorPalette } from './components/ColorPalette';
import { TypographyShowcase } from './components/TypographyShowcase';
import { ComponentShowcase } from './components/ComponentShowcase';
import { EmailTemplateGallery } from './components/EmailTemplateGallery';
import { SectionHeader } from './components/SectionHeader';

export default function App() {
  // Color data
  const primaryColors = [
    { name: 'Royal Blue', hex: '#17458F', variable: '--rotary-primary-royal-blue' },
    { name: 'Gold', hex: '#F7A81B', variable: '--rotary-primary-gold' }
  ];

  const secondaryColors = [
    { name: 'Azure', hex: '#0077C8', variable: '--rotary-secondary-azure' },
    { name: 'Sky Blue', hex: '#5BC2E7', variable: '--rotary-secondary-sky-blue' },
    { name: 'Cranberry', hex: '#B21F35', variable: '--rotary-secondary-cranberry' },
    { name: '<PERSON>', hex: '#A6192E', variable: '--rotary-secondary-cardinal' },
    { name: 'Turquoise', hex: '#00B2A9', variable: '--rotary-secondary-turquoise' },
    { name: 'Orange', hex: '#F46A25', variable: '--rotary-secondary-orange' },
    { name: 'Violet', hex: '#662D91', variable: '--rotary-secondary-violet' },
    { name: 'Grass', hex: '#78BE20', variable: '--rotary-secondary-grass' }
  ];

  const neutralColors = [
    { name: 'White', hex: '#FFFFFF', variable: '--rotary-neutral-white' },
    { name: 'Cloud', hex: '#F5F5F5', variable: '--rotary-neutral-cloud' },
    { name: 'Platinum', hex: '#E5E5E5', variable: '--rotary-neutral-platinum' },
    { name: 'Ash', hex: '#E0E0E0', variable: '--rotary-neutral-ash' },
    { name: 'Silver', hex: '#C0C0C0', variable: '--rotary-neutral-silver' },
    { name: 'Powder Blue', hex: '#C7DDEE', variable: '--rotary-neutral-powder-blue' },
    { name: 'Taupe', hex: '#BDB2A7', variable: '--rotary-neutral-taupe' },
    { name: 'Smoke', hex: '#B3B3B3', variable: '--rotary-neutral-smoke' },
    { name: 'Pewter', hex: '#9FA2A6', variable: '--rotary-neutral-pewter' },
    { name: 'Moss', hex: '#A7B79F', variable: '--rotary-neutral-moss' },
    { name: 'Stone', hex: '#8C8C8C', variable: '--rotary-neutral-stone' },
    { name: 'Storm', hex: '#7A8A9E', variable: '--rotary-neutral-storm' },
    { name: 'Slate', hex: '#6C757D', variable: '--rotary-neutral-slate' },
    { name: 'Charcoal', hex: '#4C4C4C', variable: '--rotary-neutral-charcoal' },
    { name: 'Black', hex: '#000000', variable: '--rotary-neutral-black' }
  ];

  const statusColors = [
    { name: 'Success', hex: '#78BE20', variable: '--rotary-status-success' },
    { name: 'Error', hex: '#B21F35', variable: '--rotary-status-error' },
    { name: 'Warning', hex: '#F46A25', variable: '--rotary-status-warning' },
    { name: 'Info', hex: '#5BC2E7', variable: '--rotary-status-info' }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-[#17458F] text-white py-12">
        <div className="max-w-6xl mx-auto px-6">
          <h1 className="text-h1-desktop mb-4">Rotary Design System & Email Templates</h1>
          <p className="text-body-default opacity-90">
            A comprehensive design system library featuring colors, typography, components, and professional 
            email templates for Rotary International branded communications.
          </p>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-6xl mx-auto px-6 py-12">
        {/* Overview */}
        <SectionHeader 
          title="Design System Overview" 
          subtitle="This library contains all the design tokens, components, and email templates needed to create consistent Rotary-branded experiences across digital and email communications."
        />

        {/* Color Styles */}
        <div id="colors" className="mb-16">
          <SectionHeader title="Color Styles" />
          <ColorPalette title="Primary Colors" colors={primaryColors} />
          <ColorPalette title="Secondary Colors" colors={secondaryColors} />
          <ColorPalette title="Neutral Colors" colors={neutralColors} />
          <ColorPalette title="Status Colors" colors={statusColors} />
        </div>

        {/* Typography */}
        <div id="typography" className="mb-16">
          <SectionHeader title="Text Styles" />
          <TypographyShowcase />
        </div>

        {/* Components */}
        <div id="components" className="mb-16">
          <SectionHeader title="Components" />
          <ComponentShowcase />
        </div>

        {/* Email Templates */}
        <div id="email-templates" className="mb-16">
          <EmailTemplateGallery />
        </div>

        {/* Usage Guidelines */}
        <div id="guidelines" className="mb-16">
          <SectionHeader 
            title="Usage Guidelines" 
            subtitle="Best practices for implementing the Rotary design system"
          />
          <div className="grid md:grid-cols-2 gap-6">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h4 className="text-h3-mobile mb-3 text-[#17458F]">Color Usage</h4>
              <ul className="text-body-small space-y-2 text-gray-700">
                <li>• Use Royal Blue as the primary brand color for headers and key actions</li>
                <li>• Gold should be used sparingly for accents and highlights</li>
                <li>• Secondary colors provide variety while maintaining brand consistency</li>
                <li>• Status colors should only be used for their intended semantic meaning</li>
              </ul>
            </div>
            
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h4 className="text-h3-mobile mb-3 text-[#17458F]">Typography</h4>
              <ul className="text-body-small space-y-2 text-gray-700">
                <li>• Open Sans is the primary font family for Latin text</li>
                <li>• Tajawal should be used for Arabic content with RTL support</li>
                <li>• Maintain consistent hierarchy using the defined heading styles</li>
                <li>• Ensure adequate line spacing for readability</li>
              </ul>
            </div>
            
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h4 className="text-h3-mobile mb-3 text-[#17458F]">Components</h4>
              <ul className="text-body-small space-y-2 text-gray-700">
                <li>• Use primary buttons for main actions, secondary for alternatives</li>
                <li>• Event cards should maintain consistent padding and styling</li>
                <li>• Section headers provide clear content organization</li>
                <li>• Footer should always use Royal Blue background</li>
              </ul>
            </div>
            
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h4 className="text-h3-mobile mb-3 text-[#17458F]">Accessibility</h4>
              <ul className="text-body-small space-y-2 text-gray-700">
                <li>• Ensure sufficient color contrast for text readability</li>
                <li>• Support both LTR and RTL text directions</li>
                <li>• Use semantic HTML elements for proper structure</li>
                <li>• Provide focus indicators for interactive elements</li>
              </ul>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-[#17458F] text-white py-8">
        <div className="max-w-6xl mx-auto px-6 text-center">
          <p className="text-body-default mb-2">Rotary Design System & Email Templates</p>
          <p className="text-body-small opacity-75">© 2024 Rotary International. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
}