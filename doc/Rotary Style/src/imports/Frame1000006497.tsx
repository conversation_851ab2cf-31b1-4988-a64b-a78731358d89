import imgImage940 from "figma:asset/9d167e8d54dd630ef0449d85800681a387777f6b.png";
import imgEllipse1866 from "figma:asset/272a1f8f28c77039953c162f97bf31bb4a317c1c.png";
import imgEllipse1867 from "figma:asset/c06bf8e90c1ecc5cad21fc543d009902a39ba6f9.png";
import imgEllipse1868 from "figma:asset/39c8601cb5f77d0e8fa17f120da453751ac5a830.png";
import { imgGroup, imgGroup1, imgGroup2, imgGroup3, imgGroup4, imgGroup5, imgGroup6, imgGroup7, imgLine75, imgLine76 } from "./svg-zt6xr";

function Group() {
  return (
    <div className="absolute inset-[41.94%_60.5%_55.29%_31.83%]" data-name="Group">
      <img className="block max-w-none size-full" src={imgGroup} />
    </div>
  );
}

function Group1() {
  return (
    <div className="absolute inset-[41.94%_31.83%_55.29%_61%]" data-name="Group">
      <img className="block max-w-none size-full" src={imgGroup1} />
    </div>
  );
}

function Group1000006874() {
  return (
    <div className="absolute contents inset-[41.94%_31.83%_55.29%_31.83%]">
      <Group />
      <Group1 />
    </div>
  );
}

function Group1000006875() {
  return (
    <div className="absolute contents left-[216px] top-[1360px]">
      <div className="absolute bg-[#159d71] h-[31px] rounded-[3px] top-[1360px] translate-x-[-50%] w-20" style={{ left: "calc(50% - 44px)" }} />
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] left-[256.5px] not-italic text-[13px] text-center text-nowrap text-white top-[1371px] translate-x-[-50%]">
        <p className="leading-[20px] whitespace-pre">
          <span className="uppercase">y</span>es
        </p>
      </div>
    </div>
  );
}

function Group1000006876() {
  return (
    <div className="absolute contents left-[304px] top-[1360px]">
      <div className="absolute h-[31px] rounded-[3px] top-[1360px] translate-x-[-50%] w-20" style={{ left: "calc(50% + 44px)" }}>
        <div aria-hidden="true" className="absolute border border-[#159d71] border-solid inset-0 pointer-events-none rounded-[3px]" />
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] left-[344px] not-italic text-[#159d71] text-[13px] text-center text-nowrap top-[1371px] translate-x-[-50%]">
        <p className="leading-[20px] whitespace-pre">No</p>
      </div>
    </div>
  );
}

function Group1000006877() {
  return (
    <div className="absolute contents left-1/2 top-[1360px] translate-x-[-50%]">
      <Group1000006875 />
      <Group1000006876 />
    </div>
  );
}

function Group1000006878() {
  return (
    <div className="absolute contents left-[216px] top-[1330px]">
      <Group1000006877 />
      <div className="absolute font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic text-[#159d71] text-[20px] text-nowrap top-[1330px]" style={{ left: "calc(50% - 77px)" }}>
        <p className="leading-[30px] whitespace-pre">Will you join us?</p>
      </div>
    </div>
  );
}

function Group2() {
  return (
    <div className="absolute inset-[57.29%_77.17%_40.45%_17%]" data-name="Group">
      <img className="block max-w-none size-full" src={imgGroup2} />
    </div>
  );
}

function Group3() {
  return (
    <div className="absolute inset-[57.29%_47.17%_40.48%_47%]" data-name="Group">
      <img className="block max-w-none size-full" src={imgGroup3} />
    </div>
  );
}

function Group4() {
  return (
    <div className="absolute inset-[57.61%_16.67%_40.75%_77.5%]" data-name="Group">
      <img className="block max-w-none size-full" src={imgGroup4} />
    </div>
  );
}

function Group1000006879() {
  return (
    <div className="absolute contents font-['Poppins:Regular',_sans-serif] leading-[0] left-1/2 not-italic text-[10px] text-nowrap text-white top-[1477px] translate-x-[-50%]">
      <div className="absolute top-[1477px]" style={{ left: "calc(50% - 88px)" }}>
        <p className="[text-decoration-skip-ink:none] [text-underline-position:from-font] decoration-solid leading-[20px] text-nowrap underline whitespace-pre">Manage Preferences</p>
      </div>
      <div className="absolute top-[1477px]" style={{ left: "calc(50% + 26px)" }}>
        <p className="[text-decoration-skip-ink:none] [text-underline-position:from-font] decoration-solid leading-[20px] text-nowrap underline whitespace-pre">Unsubscribe</p>
      </div>
    </div>
  );
}

function Group5() {
  return (
    <div className="absolute inset-[97.23%_53.42%_1.33%_42.83%]" data-name="Group">
      <img className="block max-w-none size-full" src={imgGroup5} />
    </div>
  );
}

function Group6() {
  return (
    <div className="absolute inset-[97.23%_47.99%_1.33%_48.27%]" data-name="Group">
      <img className="block max-w-none size-full" src={imgGroup6} />
    </div>
  );
}

function Group7() {
  return (
    <div className="absolute inset-[-0.01%_-0.02%_0.01%_0.02%]" data-name="Group">
      <img className="block max-w-none size-full" src={imgGroup7} />
    </div>
  );
}

function Frame() {
  return (
    <div className="absolute inset-[97.23%_42.67%_1.33%_53.59%] overflow-clip" data-name="Frame">
      <Group7 />
    </div>
  );
}

function Group1000004411() {
  return (
    <div className="absolute contents inset-[97.23%_42.67%_1.33%_42.83%]">
      <Group5 />
      <Group6 />
      <Frame />
    </div>
  );
}

export default function Frame1000006497() {
  return (
    <div className="bg-white relative size-full">
      <div className="absolute bg-[#fcfcfc] h-[110px] left-0 top-[630px] w-[600px]" />
      <div className="absolute bg-[#fcfcfc] h-[103px] left-0 top-[1309px] w-[600px]" />
      <div className="absolute font-['Anton:Regular',_sans-serif] leading-[0] not-italic text-[0px] text-center text-nowrap text-white top-[19px] translate-x-[-50%]" style={{ left: "calc(50% + 0.5px)" }}>
        <p className="leading-[normal] whitespace-pre">
          <span className="text-[26.833px]">L</span>
          <span className="text-[23.541px]">O</span>
          <span className="text-[26.833px]">G</span>
          <span className="text-[23.541px]">O</span>
        </p>
      </div>
      <div className="absolute bg-[#159d71] h-[66px] left-1/2 top-[13px] translate-x-[-50%] w-[520px]" />
      <div className="absolute bg-[#159d71] h-[37px] rounded-[3px] top-[568px] translate-x-[-50%] w-[141px]" style={{ left: "calc(50% + 0.5px)" }} />
      <div className="absolute bg-[#278b5d] h-[25px] left-1/2 top-[79px] translate-x-[-50%] w-[520px]" />
      <div className="absolute font-['Inter:Bold',_sans-serif] font-bold leading-[0] not-italic text-[24px] text-nowrap text-white top-8" style={{ left: "calc(50% - 29px)" }}>
        <p className="leading-[normal] whitespace-pre">Logo</p>
      </div>
      <div className="absolute font-['Poppins:Light',_sans-serif] leading-[0] left-[69px] not-italic text-[11px] text-nowrap text-white top-[88px]">
        <p className="leading-[20px] whitespace-pre">Learn the latest strategies from the experts and elevate your digital marketing game!</p>
      </div>
      <div className="absolute bg-center bg-cover bg-no-repeat h-[322px] left-1/2 top-28 translate-x-[-50%] w-[504px]" data-name="image 940" style={{ backgroundImage: `url('${imgImage940}')` }} />
      <div className="absolute bg-[#159d71] h-[73px] left-1/2 opacity-[0.15] top-[431px] translate-x-[-50%] w-[600px]" />
      <div className="absolute font-['Inter:Bold',_sans-serif] font-bold leading-[0] not-italic text-[#159d71] text-[20px] text-center top-[443px] translate-x-[-50%] w-[503px]" style={{ left: "calc(50% - 1.5px)" }}>
        <p className="leading-[25px]">Streamline Your Success with Our Live Webinar: Digital Marketing Breakthroughs!</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] left-1/2 not-italic text-[#282828] text-[12px] text-center top-[519px] translate-x-[-50%] w-[422px]">
        <p className="leading-[20px]">Harness the power of innovative marketing techniques to revolutionize your brand’s online presence.</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[#282828] text-[12px] text-center text-nowrap top-[711px] translate-x-[-50%]" style={{ left: "calc(50% - 87px)" }}>
        <p className="leading-[20px] whitespace-pre">DATE: August 12th, 2023</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[#282828] text-[12px] text-center text-nowrap top-[711px] translate-x-[-50%]" style={{ left: "calc(50% + 87.5px)" }}>
        <p className="leading-[20px] whitespace-pre">TIME: 1:00PM PST</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] left-[301px] not-italic text-[13px] text-center text-nowrap text-white top-[582px] translate-x-[-50%]">
        <p className="leading-[20px] whitespace-pre">Secure My Spot</p>
      </div>
      <Group1000006874 />
      <div className="absolute flex h-[34px] items-center justify-center left-1/2 top-[659px] translate-x-[-50%] w-[0px]">
        <div className="flex-none rotate-[90deg]">
          <div className="h-0 relative w-[34px]">
            <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
              <img className="block max-w-none size-full" src={imgLine75} />
            </div>
          </div>
        </div>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[#282828] text-[12px] top-[799px] w-[520px]" style={{ left: "calc(50% - 260px)" }}>
        <p className="leading-[20px]">Unlock the potential of digital marketing and stay ahead of the curve with our insider knowledge and strategy showcase.</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[#282828] text-[12px] text-center top-[942px] translate-x-[-50%] w-[157px]" style={{ left: "calc(50% - 180.5px)" }}>
        <p className="leading-[20px]">The latest trends reshaping the digital landscape and how to leverage them.</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[#282828] text-[10px] text-center top-[1225px] translate-x-[-50%] w-[145px]" style={{ left: "calc(50% - 189.5px)" }}>
        <p className="leading-[16px]">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[#282828] text-[10px] text-center top-[1225px] translate-x-[-50%] w-[145px]" style={{ left: "calc(50% - 5.5px)" }}>
        <p className="leading-[16px]">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[#282828] text-[10px] text-center top-[1225px] translate-x-[-50%] w-[145px]" style={{ left: "calc(50% + 187.5px)" }}>
        <p className="leading-[16px]">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[20px] not-italic text-[#282828] text-[12px] text-center top-[942px] translate-x-[-50%] w-[157px]" style={{ left: "calc(50% - 0.5px)" }}>
        <p className="mb-0">{`Cutting-edge tools `}</p>
        <p>and platforms that will enhance your marketing efficiency.</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[#282828] text-[12px] text-center top-[942px] translate-x-[-50%] w-[143px]" style={{ left: "calc(50% + 181.5px)" }}>
        <p className="leading-[20px]">Networking strategies to connect with influencers and amplify your reach.</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[#159d71] text-[12px] top-[847px] w-[520px]" style={{ left: "calc(50% - 260px)" }}>
        <p className="leading-[20px]">{`What You'll Discover:`}</p>
      </div>
      <div className="absolute font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic text-[#159d71] text-[20px] text-nowrap top-[766px]" style={{ left: "calc(50% - 260px)" }}>
        <p className="leading-[30px] whitespace-pre">Inside the Webinar</p>
      </div>
      <div className="absolute font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic text-[#159d71] text-[20px] text-nowrap top-[1055px]" style={{ left: "calc(50% - 260px)" }}>
        <p className="leading-[30px] whitespace-pre">Speakers</p>
      </div>
      <Group1000006878 />
      <Group2 />
      <Group3 />
      <Group4 />
      <div className="absolute flex h-[48px] items-center justify-center top-[1110px] translate-x-[-50%] w-[0px]" style={{ left: "calc(50% - 106px)" }}>
        <div className="flex-none rotate-[90deg]">
          <div className="h-0 relative w-12">
            <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
              <img className="block max-w-none size-full" src={imgLine76} />
            </div>
          </div>
        </div>
      </div>
      <div className="absolute flex h-[48px] items-center justify-center top-[1110px] translate-x-[-50%] w-[0px]" style={{ left: "calc(50% + 96px)" }}>
        <div className="flex-none rotate-[90deg]">
          <div className="h-0 relative w-12">
            <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
              <img className="block max-w-none size-full" src={imgLine76} />
            </div>
          </div>
        </div>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[#159d71] text-[9px] text-center text-nowrap top-[1205px] translate-x-[-50%]" style={{ left: "calc(50% - 190px)" }}>
        <p className="leading-[20px] whitespace-pre">Digital Marketing Strategist</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[#159d71] text-[9px] text-center text-nowrap top-[1205px] translate-x-[-50%]" style={{ left: "calc(50% - 5.5px)" }}>
        <p className="leading-[20px] whitespace-pre">Branding Expert</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[#159d71] text-[9px] text-center text-nowrap top-[1205px] translate-x-[-50%]" style={{ left: "calc(50% + 188px)" }}>
        <p className="leading-[20px] whitespace-pre">{`SEO & Analytics Guru`}</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[12px] text-black text-nowrap top-[1186px]" style={{ left: "calc(50% - 234px)" }}>
        <p className="leading-[20px] whitespace-pre">Julianne Grant</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[12px] text-black text-center text-nowrap top-[1186px] translate-x-[-50%]" style={{ left: "calc(50% - 5.5px)" }}>
        <p className="leading-[20px] whitespace-pre">Lucas Werner</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[12px] text-black text-center text-nowrap top-[1186px] translate-x-[-50%]" style={{ left: "calc(50% + 187.5px)" }}>
        <p className="leading-[20px] whitespace-pre">Nadia Patel</p>
      </div>
      <div className="absolute left-[72px] size-[77px] top-[1095px]">
        <img className="block max-w-none size-full" height="77" src={imgEllipse1866} width="77" />
      </div>
      <div className="absolute left-64 size-[77px] top-[1095px]">
        <img className="block max-w-none size-full" height="77" src={imgEllipse1867} width="77" />
      </div>
      <div className="absolute left-[449px] size-[77px] top-[1095px]">
        <img className="block max-w-none size-full" height="77" src={imgEllipse1868} width="77" />
      </div>
      <div className="absolute bg-[#278b5d] h-[138px] left-1/2 top-[1412px] translate-x-[-50%] w-[600px]" />
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[11px] text-nowrap text-white top-[1436px]" style={{ left: "calc(50% - 152px)" }}>
        <p className="leading-[20px] whitespace-pre">{`Home     |     Support     |     Our Policy     |     Member Area`}</p>
      </div>
      <div className="absolute font-['Poppins:Regular',_sans-serif] leading-[0] not-italic text-[11px] text-nowrap text-white top-[1456px]" style={{ left: "calc(50% - 199px)" }}>
        <p className="leading-[20px] whitespace-pre">You’re receiving this email because you signed up for our webinar alerts.</p>
      </div>
      <Group1000006879 />
      <Group1000004411 />
    </div>
  );
}