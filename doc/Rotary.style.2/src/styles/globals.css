@custom-variant dark (&:is(.dark *));

:root {
  --font-size: 16px;
  --background: #ffffff;
  --foreground: oklch(0.145 0 0);
  --card: #ffffff;
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: #030213;
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.95 0.0058 264.53);
  --secondary-foreground: #030213;
  --muted: #ececf0;
  --muted-foreground: #717182;
  --accent: #e9ebef;
  --accent-foreground: #030213;
  --destructive: #d4183d;
  --destructive-foreground: #ffffff;
  --border: rgba(0, 0, 0, 0.1);
  --input: transparent;
  --input-background: #f3f3f5;
  --switch-background: #cbced4;
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: #030213;
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);

  /* Rotary Color System */
  /* Primary Colors */
  --rotary-primary-royal-blue: #17458F;
  --rotary-primary-gold: #F7A81B;
  
  /* Secondary Colors */
  --rotary-secondary-azure: #0077C8;
  --rotary-secondary-sky-blue: #5BC2E7;
  --rotary-secondary-cranberry: #B21F35;
  --rotary-secondary-cardinal: #A6192E;
  --rotary-secondary-turquoise: #00B2A9;
  --rotary-secondary-orange: #F46A25;
  --rotary-secondary-violet: #662D91;
  --rotary-secondary-grass: #78BE20;
  
  /* Neutral Colors */
  --rotary-neutral-white: #FFFFFF;
  --rotary-neutral-black: #000000;
  --rotary-neutral-charcoal: #4C4C4C;
  --rotary-neutral-slate: #6C757D;
  --rotary-neutral-stone: #8C8C8C;
  --rotary-neutral-pewter: #9FA2A6;
  --rotary-neutral-smoke: #B3B3B3;
  --rotary-neutral-silver: #C0C0C0;
  --rotary-neutral-powder-blue: #C7DDEE;
  --rotary-neutral-moss: #A7B79F;
  --rotary-neutral-taupe: #BDB2A7;
  --rotary-neutral-storm: #7A8A9E;
  --rotary-neutral-ash: #E0E0E0;
  --rotary-neutral-platinum: #E5E5E5;
  --rotary-neutral-cloud: #F5F5F5;
  
  /* Status Colors */
  --rotary-status-success: #78BE20;
  --rotary-status-error: #B21F35;
  --rotary-status-warning: #F46A25;
  --rotary-status-info: #5BC2E7;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --font-weight-medium: 500;
  --font-weight-normal: 400;
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-input-background: var(--input-background);
  --color-switch-background: var(--switch-background);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  /* Rotary Color Tokens */
  --color-rotary-primary-royal-blue: var(--rotary-primary-royal-blue);
  --color-rotary-primary-gold: var(--rotary-primary-gold);
  --color-rotary-secondary-azure: var(--rotary-secondary-azure);
  --color-rotary-secondary-sky-blue: var(--rotary-secondary-sky-blue);
  --color-rotary-secondary-cranberry: var(--rotary-secondary-cranberry);
  --color-rotary-secondary-cardinal: var(--rotary-secondary-cardinal);
  --color-rotary-secondary-turquoise: var(--rotary-secondary-turquoise);
  --color-rotary-secondary-orange: var(--rotary-secondary-orange);
  --color-rotary-secondary-violet: var(--rotary-secondary-violet);
  --color-rotary-secondary-grass: var(--rotary-secondary-grass);
  --color-rotary-neutral-white: var(--rotary-neutral-white);
  --color-rotary-neutral-black: var(--rotary-neutral-black);
  --color-rotary-neutral-charcoal: var(--rotary-neutral-charcoal);
  --color-rotary-neutral-slate: var(--rotary-neutral-slate);
  --color-rotary-neutral-stone: var(--rotary-neutral-stone);
  --color-rotary-neutral-pewter: var(--rotary-neutral-pewter);
  --color-rotary-neutral-smoke: var(--rotary-neutral-smoke);
  --color-rotary-neutral-silver: var(--rotary-neutral-silver);
  --color-rotary-neutral-powder-blue: var(--rotary-neutral-powder-blue);
  --color-rotary-neutral-moss: var(--rotary-neutral-moss);
  --color-rotary-neutral-taupe: var(--rotary-neutral-taupe);
  --color-rotary-neutral-storm: var(--rotary-neutral-storm);
  --color-rotary-neutral-ash: var(--rotary-neutral-ash);
  --color-rotary-neutral-platinum: var(--rotary-neutral-platinum);
  --color-rotary-neutral-cloud: var(--rotary-neutral-cloud);
  --color-rotary-status-success: var(--rotary-status-success);
  --color-rotary-status-error: var(--rotary-status-error);
  --color-rotary-status-warning: var(--rotary-status-warning);
  --color-rotary-status-info: var(--rotary-status-info);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/**
 * Base typography. This is not applied to elements which have an ancestor with a Tailwind text class.
 */
@layer base {
  :where(:not(:has([class*=" text-"]), :not(:has([class^="text-"])))) {
    h1 {
      font-size: var(--text-2xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h2 {
      font-size: var(--text-xl);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h3 {
      font-size: var(--text-lg);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    h4 {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    p {
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }

    label {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    button {
      font-size: var(--text-base);
      font-weight: var(--font-weight-medium);
      line-height: 1.5;
    }

    input {
      font-size: var(--text-base);
      font-weight: var(--font-weight-normal);
      line-height: 1.5;
    }
  }
}

html {
  font-size: var(--font-size);
}

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

/* Rotary Typography Styles */
.text-h1-desktop {
  font-family: 'Open Sans', Arial, sans-serif;
  font-size: 36px;
  font-weight: 700;
  line-height: 44px;
}

.text-h1-mobile {
  font-family: 'Open Sans', Arial, sans-serif;
  font-size: 28px;
  font-weight: 700;
  line-height: 34px;
}

.text-h2-desktop {
  font-family: 'Open Sans', Arial, sans-serif;
  font-size: 28px;
  font-weight: 600;
  line-height: 34px;
}

.text-h2-mobile {
  font-family: 'Open Sans', Arial, sans-serif;
  font-size: 22px;
  font-weight: 600;
  line-height: 28px;
}

.text-h3-desktop {
  font-family: 'Open Sans', Arial, sans-serif;
  font-size: 22px;
  font-weight: 600;
  line-height: 28px;
}

.text-h3-mobile {
  font-family: 'Open Sans', Arial, sans-serif;
  font-size: 18px;
  font-weight: 600;
  line-height: 24px;
}

.text-body-default {
  font-family: 'Open Sans', Arial, sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
}

.text-body-small {
  font-family: 'Open Sans', Arial, sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
}

.text-caption-meta {
  font-family: 'Open Sans', Arial, sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
}

/* Arabic Typography Styles */
.text-h1-ar {
  font-family: 'Tajawal', sans-serif;
  font-size: 36px;
  font-weight: 700;
  line-height: 44px;
  direction: rtl;
  text-align: right;
}

.text-h2-ar {
  font-family: 'Tajawal', sans-serif;
  font-size: 28px;
  font-weight: 600;
  line-height: 34px;
  direction: rtl;
  text-align: right;
}

.text-h3-ar {
  font-family: 'Tajawal', sans-serif;
  font-size: 22px;
  font-weight: 600;
  line-height: 28px;
  direction: rtl;
  text-align: right;
}

.text-body-ar {
  font-family: 'Tajawal', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 24px;
  direction: rtl;
  text-align: right;
}

.text-caption-ar {
  font-family: 'Tajawal', sans-serif;
  font-size: 12px;
  font-weight: 400;
  line-height: 16px;
  direction: rtl;
  text-align: right;
}
