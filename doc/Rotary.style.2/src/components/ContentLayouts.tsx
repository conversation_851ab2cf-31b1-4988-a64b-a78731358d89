import { RotaryButton } from './RotaryButton';
import { ImageWithFallback } from './figma/ImageWithFallback';

// Feature Grid Component
interface Feature {
  icon: string;
  title: string;
  description: string;
  link?: string;
}

interface FeatureGridProps {
  title?: string;
  subtitle?: string;
  features: Feature[];
  columns?: 2 | 3 | 4;
}

export function FeatureGrid({ title, subtitle, features, columns = 3 }: FeatureGridProps) {
  const gridCols = {
    2: 'grid-cols-1 md:grid-cols-2',
    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
  };

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-6">
        {(title || subtitle) && (
          <div className="text-center mb-12">
            {title && <h2 className="text-h2-desktop text-[#17458F] mb-4">{title}</h2>}
            {subtitle && <p className="text-body-default text-gray-600 max-w-3xl mx-auto">{subtitle}</p>}
          </div>
        )}
        
        <div className={`grid ${gridCols[columns]} gap-8`}>
          {features.map((feature, index) => (
            <div key={index} className="text-center group">
              <div className="w-16 h-16 mx-auto mb-4 text-4xl text-[#F7A81B] group-hover:text-[#17458F] transition-colors">
                {feature.icon}
              </div>
              <h3 className="text-h3-desktop text-[#17458F] mb-3">{feature.title}</h3>
              <p className="text-body-default text-gray-600 mb-4">{feature.description}</p>
              {feature.link && (
                <a href={feature.link} className="text-[#F7A81B] hover:text-[#17458F] text-body-small font-medium">
                  Learn More →
                </a>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

// Two Column Content Component
interface TwoColumnContentProps {
  title: string;
  content: string;
  imageSrc: string;
  imageAlt: string;
  imagePosition?: 'left' | 'right';
  ctaText?: string;
  ctaLink?: string;
  backgroundColor?: 'white' | 'gray' | 'blue';
}

export function TwoColumnContent({
  title,
  content,
  imageSrc,
  imageAlt,
  imagePosition = 'right',
  ctaText,
  ctaLink,
  backgroundColor = 'white'
}: TwoColumnContentProps) {
  const bgColors = {
    white: 'bg-white',
    gray: 'bg-gray-50',
    blue: 'bg-[#17458F] bg-opacity-5'
  };

  const textColor = backgroundColor === 'blue' ? 'text-[#17458F]' : 'text-gray-600';

  return (
    <section className={`py-16 ${bgColors[backgroundColor]}`}>
      <div className="max-w-7xl mx-auto px-6">
        <div className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${imagePosition === 'left' ? 'lg:grid-flow-col-dense' : ''}`}>
          <div className={imagePosition === 'left' ? 'lg:col-start-2' : ''}>
            <h2 className="text-h2-desktop text-[#17458F] mb-6">{title}</h2>
            <div className={`text-body-default ${textColor} mb-6 space-y-4`}>
              {content.split('\n\n').map((paragraph, index) => (
                <p key={index}>{paragraph}</p>
              ))}
            </div>
            {ctaText && ctaLink && (
              <RotaryButton variant="primary">
                <a href={ctaLink}>{ctaText}</a>
              </RotaryButton>
            )}
          </div>
          <div className={imagePosition === 'left' ? 'lg:col-start-1' : ''}>
            <ImageWithFallback
              src={imageSrc}
              alt={imageAlt}
              className="w-full h-[400px] object-cover rounded-lg shadow-lg"
            />
          </div>
        </div>
      </div>
    </section>
  );
}

// Stats Section Component
interface Stat {
  value: string;
  label: string;
  description?: string;
}

interface StatsProps {
  title?: string;
  subtitle?: string;
  stats: Stat[];
  backgroundColor?: 'white' | 'blue' | 'gold';
}

export function Stats({ title, subtitle, stats, backgroundColor = 'blue' }: StatsProps) {
  const bgConfig = {
    white: { bg: 'bg-white', titleColor: 'text-[#17458F]', valueColor: 'text-[#F7A81B]', textColor: 'text-gray-600' },
    blue: { bg: 'bg-[#17458F]', titleColor: 'text-white', valueColor: 'text-[#F7A81B]', textColor: 'text-white' },
    gold: { bg: 'bg-[#F7A81B]', titleColor: 'text-[#17458F]', valueColor: 'text-white', textColor: 'text-[#17458F]' }
  };

  const config = bgConfig[backgroundColor];

  return (
    <section className={`py-16 ${config.bg}`}>
      <div className="max-w-7xl mx-auto px-6">
        {(title || subtitle) && (
          <div className="text-center mb-12">
            {title && <h2 className={`text-h2-desktop ${config.titleColor} mb-4`}>{title}</h2>}
            {subtitle && <p className={`text-body-default ${config.textColor} max-w-3xl mx-auto`}>{subtitle}</p>}
          </div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className={`text-h1-desktop ${config.valueColor} mb-2`}>{stat.value}</div>
              <div className={`text-h3-mobile ${config.titleColor} mb-2`}>{stat.label}</div>
              {stat.description && (
                <p className={`text-body-small ${config.textColor} opacity-90`}>{stat.description}</p>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

// Call to Action Section
interface CTASectionProps {
  title: string;
  subtitle?: string;
  primaryCTA: string;
  secondaryCTA?: string;
  backgroundImage?: string;
  overlay?: boolean;
}

export function CTASection({
  title,
  subtitle,
  primaryCTA,
  secondaryCTA,
  backgroundImage,
  overlay = true
}: CTASectionProps) {
  return (
    <section className="relative py-20">
      {backgroundImage && (
        <>
          <ImageWithFallback
            src={backgroundImage}
            alt="Background"
            className="absolute inset-0 w-full h-full object-cover"
          />
          {overlay && <div className="absolute inset-0 bg-[#17458F] bg-opacity-80" />}
        </>
      )}
      
      <div className="relative z-10 max-w-4xl mx-auto px-6 text-center">
        <h2 className="text-h1-desktop text-white mb-6">{title}</h2>
        {subtitle && (
          <p className="text-body-default text-white opacity-90 mb-8 max-w-2xl mx-auto">{subtitle}</p>
        )}
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <RotaryButton variant="primary" className="bg-[#F7A81B] text-[#17458F] hover:bg-yellow-400">
            {primaryCTA}
          </RotaryButton>
          {secondaryCTA && (
            <RotaryButton variant="secondary" className="text-white border-white hover:bg-white hover:text-[#17458F]">
              {secondaryCTA}
            </RotaryButton>
          )}
        </div>
      </div>
    </section>
  );
}

// News/Blog Card Grid
interface NewsItem {
  title: string;
  excerpt: string;
  date: string;
  category: string;
  image: string;
  link: string;
}

interface NewsGridProps {
  title?: string;
  subtitle?: string;
  items: NewsItem[];
  showAll?: boolean;
}

export function NewsGrid({ title, subtitle, items, showAll = false }: NewsGridProps) {
  const displayItems = showAll ? items : items.slice(0, 3);

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-6">
        {(title || subtitle) && (
          <div className="text-center mb-12">
            {title && <h2 className="text-h2-desktop text-[#17458F] mb-4">{title}</h2>}
            {subtitle && <p className="text-body-default text-gray-600 max-w-3xl mx-auto">{subtitle}</p>}
          </div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {displayItems.map((item, index) => (
            <article key={index} className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
              <ImageWithFallback
                src={item.image}
                alt={item.title}
                className="w-full h-48 object-cover"
              />
              <div className="p-6">
                <div className="flex items-center mb-3">
                  <span className="text-caption-meta text-[#F7A81B] font-medium">{item.category}</span>
                  <span className="mx-2 text-gray-300">•</span>
                  <span className="text-caption-meta text-gray-500">{item.date}</span>
                </div>
                <h3 className="text-h3-mobile text-[#17458F] mb-3 hover:text-[#F7A81B] transition-colors">
                  <a href={item.link}>{item.title}</a>
                </h3>
                <p className="text-body-small text-gray-600 mb-4">{item.excerpt}</p>
                <a 
                  href={item.link}
                  className="text-[#F7A81B] hover:text-[#17458F] text-body-small font-medium"
                >
                  Read More →
                </a>
              </div>
            </article>
          ))}
        </div>
        
        {!showAll && items.length > 3 && (
          <div className="text-center mt-12">
            <RotaryButton variant="secondary">
              View All News
            </RotaryButton>
          </div>
        )}
      </div>
    </section>
  );
}