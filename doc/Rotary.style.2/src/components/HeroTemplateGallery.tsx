import { HeroPageTemplate } from './HeroPageTemplate';
import { SectionHeader } from './SectionHeader';

export function HeroTemplateGallery() {
  const templates = [
    {
      id: 'service',
      title: 'Service & Community Template',
      description: 'Perfect for club homepages emphasizing community service and member recruitment',
      variant: 'service' as const,
      props: {
        variant: 'service' as const,
        textAlignment: 'center' as const,
        showStats: true,
        primaryCTA: "Join Our Club",
        secondaryCTA: "Learn More"
      }
    },
    {
      id: 'business',
      title: 'Professional Event Template',
      description: 'Ideal for district conferences, business networking events, and professional gatherings',
      variant: 'business' as const,
      props: {
        variant: 'business' as const,
        textAlignment: 'center' as const,
        showStats: false,
        primaryCTA: "Register Now",
        secondaryCTA: "View Agenda"
      }
    },
    {
      id: 'fundraising',
      title: 'Fundraising Campaign Template',
      description: 'Great for charity galas, fundraising campaigns, and humanitarian project launches',
      variant: 'fundraising' as const,
      props: {
        variant: 'fundraising' as const,
        textAlignment: 'center' as const,
        showStats: false,
        primaryCTA: "Donate Now",
        secondaryCTA: "Learn About Impact"
      }
    }
  ];

  const customizationOptions = [
    {
      category: "Layout Options",
      options: [
        "Text alignment (left, center, right)",
        "Background image customization",
        "Overlay opacity control",
        "Statistics display toggle",
        "Navigation menu visibility"
      ]
    },
    {
      category: "Content Customization",
      options: [
        "Custom titles and subtitles",
        "Personalized descriptions",
        "Call-to-action button text",
        "Statistics values and labels",
        "Club/organization branding"
      ]
    },
    {
      category: "Visual Elements",
      options: [
        "Hero background images",
        "Rotary brand color integration",
        "Typography hierarchy",
        "Button styling variations",
        "Responsive design adaptation"
      ]
    }
  ];

  return (
    <div className="space-y-12">
      <SectionHeader 
        title="Hero Page Templates"
        subtitle="Stunning landing page heroes designed with Rotary brand guidelines for maximum impact and engagement"
      />
      
      {templates.map((template, index) => (
        <div key={template.id} className="space-y-6">
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="text-h3-desktop text-[#17458F] mb-2">{template.title}</h3>
            <p className="text-body-default text-gray-600 mb-4">{template.description}</p>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="text-body-default font-medium mb-2">Template Features:</h4>
              <ul className="text-body-small text-gray-600 space-y-1">
                <li>• Full-screen responsive hero layout</li>
                <li>• Integrated Rotary branding with Royal Blue and Gold</li>
                <li>• Professional typography using Open Sans</li>
                <li>• Customizable background images with overlay</li>
                <li>• Clear call-to-action buttons using Rotary design system</li>
                <li>• Optional statistics showcase section</li>
                <li>• Smooth scroll indicators and navigation</li>
              </ul>
            </div>
          </div>
          
          {/* Hero Template Preview */}
          <div className="bg-gray-100 rounded-lg overflow-hidden shadow-lg">
            <div className="relative h-[600px]">
              <HeroPageTemplate {...template.props} />
            </div>
          </div>
          
          {/* Implementation Guide */}
          <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
            <h4 className="text-h3-mobile text-[#17458F] mb-3">Implementation Guide</h4>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h5 className="text-body-default font-medium mb-2">Key Components:</h5>
                <ul className="text-body-small text-gray-700 space-y-1">
                  <li>• Background image with Royal Blue overlay</li>
                  <li>• Rotary navigation header</li>
                  <li>• Hero content with title hierarchy</li>
                  <li>• Gold accent badge for subtitles</li>
                  <li>• Primary and secondary CTA buttons</li>
                  <li>• Optional statistics display</li>
                </ul>
              </div>
              <div>
                <h5 className="text-body-default font-medium mb-2">Usage Scenarios:</h5>
                <ul className="text-body-small text-gray-700 space-y-1">
                  <li>• Club website homepages</li>
                  <li>• Event landing pages</li>
                  <li>• Membership recruitment campaigns</li>
                  <li>• Fundraising project launches</li>
                  <li>• District conference promotions</li>
                  <li>• Community service showcases</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      ))}
      
      {/* Customization Options */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-h3-desktop text-[#17458F] mb-6">Customization Options</h3>
        <div className="grid md:grid-cols-3 gap-6">
          {customizationOptions.map((category, index) => (
            <div key={index}>
              <h4 className="text-body-default font-medium mb-3 text-[#F7A81B]">{category.category}</h4>
              <ul className="text-body-small text-gray-600 space-y-2">
                {category.options.map((option, optionIndex) => (
                  <li key={optionIndex}>• {option}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>

      {/* Technical Specifications */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-h3-desktop text-[#17458F] mb-4">Technical Specifications</h3>
        <div className="grid md:grid-cols-4 gap-6">
          <div>
            <h4 className="text-body-default font-medium mb-2">Dimensions</h4>
            <ul className="text-body-small text-gray-600 space-y-1">
              <li>• Full viewport height</li>
              <li>• Responsive breakpoints</li>
              <li>• Mobile-optimized layout</li>
            </ul>
          </div>
          <div>
            <h4 className="text-body-default font-medium mb-2">Colors</h4>
            <ul className="text-body-small text-gray-600 space-y-1">
              <li>• Primary: Royal Blue (#17458F)</li>
              <li>• Accent: Gold (#F7A81B)</li>
              <li>• Text: White overlay</li>
            </ul>
          </div>
          <div>
            <h4 className="text-body-default font-medium mb-2">Typography</h4>
            <ul className="text-body-small text-gray-600 space-y-1">
              <li>• Font: Open Sans</li>
              <li>• H1: 48px desktop</li>
              <li>• Body: 18px large screens</li>
            </ul>
          </div>
          <div>
            <h4 className="text-body-default font-medium mb-2">Features</h4>
            <ul className="text-body-small text-gray-600 space-y-1">
              <li>• Background overlay</li>
              <li>• Scroll indicators</li>
              <li>• Statistics display</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Best Practices */}
      <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-200">
        <h3 className="text-h3-desktop text-[#17458F] mb-4">Best Practices</h3>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-body-default font-medium mb-2 text-[#F7A81B]">Content Guidelines:</h4>
            <ul className="text-body-small text-gray-700 space-y-1">
              <li>• Keep headlines concise and impactful (6-8 words)</li>
              <li>• Use action-oriented language in CTAs</li>
              <li>• Ensure descriptions are scannable (2-3 lines max)</li>
              <li>• Include relevant statistics when available</li>
            </ul>
          </div>
          <div>
            <h4 className="text-body-default font-medium mb-2 text-[#F7A81B]">Design Principles:</h4>
            <ul className="text-body-small text-gray-700 space-y-1">
              <li>• Maintain high contrast for text readability</li>
              <li>• Use high-quality, relevant background images</li>
              <li>• Test across different screen sizes</li>
              <li>• Ensure fast loading times for images</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}