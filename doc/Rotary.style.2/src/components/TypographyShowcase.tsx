export function TypographyShowcase() {
  return (
    <div className="mb-8">
      <h3 className="text-h3-desktop mb-6">Typography Styles</h3>
      
      {/* Latin Typography */}
      <div className="mb-8">
        <h4 className="text-h3-mobile mb-4 text-gray-700">Latin (Open Sans)</h4>
        <div className="space-y-4">
          <div className="border-l-4 border-blue-500 pl-4">
            <p className="text-caption-meta text-gray-600 mb-1">H1 / Desktop → 36px, Bold, Line height 44px</p>
            <h1 className="text-h1-desktop">This is a sample heading 1</h1>
          </div>
          
          <div className="border-l-4 border-blue-500 pl-4">
            <p className="text-caption-meta text-gray-600 mb-1">H1 / Mobile → 28px, Bold, Line height 34px</p>
            <h1 className="text-h1-mobile">This is a sample heading 1</h1>
          </div>
          
          <div className="border-l-4 border-green-500 pl-4">
            <p className="text-caption-meta text-gray-600 mb-1">H2 / Desktop → 28px, SemiBold, Line height 34px</p>
            <h2 className="text-h2-desktop">This is a sample heading 2</h2>
          </div>
          
          <div className="border-l-4 border-green-500 pl-4">
            <p className="text-caption-meta text-gray-600 mb-1">H2 / Mobile → 22px, SemiBold, Line height 28px</p>
            <h2 className="text-h2-mobile">This is a sample heading 2</h2>
          </div>
          
          <div className="border-l-4 border-yellow-500 pl-4">
            <p className="text-caption-meta text-gray-600 mb-1">H3 / Desktop → 22px, SemiBold, Line height 28px</p>
            <h3 className="text-h3-desktop">This is a sample heading 3</h3>
          </div>
          
          <div className="border-l-4 border-yellow-500 pl-4">
            <p className="text-caption-meta text-gray-600 mb-1">H3 / Mobile → 18px, SemiBold, Line height 24px</p>
            <h3 className="text-h3-mobile">This is a sample heading 3</h3>
          </div>
          
          <div className="border-l-4 border-gray-500 pl-4">
            <p className="text-caption-meta text-gray-600 mb-1">Body / Default → 16px, Regular, Line height 24px</p>
            <p className="text-body-default">This is sample body text that demonstrates the default body style. It should be easy to read and have appropriate spacing for comfortable reading.</p>
          </div>
          
          <div className="border-l-4 border-gray-500 pl-4">
            <p className="text-caption-meta text-gray-600 mb-1">Body / Small → 14px, Regular, Line height 20px</p>
            <p className="text-body-small">This is sample small body text that can be used for secondary information or captions.</p>
          </div>
          
          <div className="border-l-4 border-gray-500 pl-4">
            <p className="text-caption-meta text-gray-600 mb-1">Caption / Meta → 12px, Regular, Line height 16px</p>
            <p className="text-caption-meta">This is caption text for metadata and fine print.</p>
          </div>
        </div>
      </div>

      {/* Arabic Typography */}
      <div>
        <h4 className="text-h3-mobile mb-4 text-gray-700">Arabic (Tajawal)</h4>
        <div className="space-y-4">
          <div className="border-r-4 border-blue-500 pr-4">
            <p className="text-caption-meta text-gray-600 mb-1 text-right">H1 / AR → 36px, Bold, Line height 44px, RTL enabled</p>
            <h1 className="text-h1-ar">هذا عنوان تجريبي من المستوى الأول</h1>
          </div>
          
          <div className="border-r-4 border-green-500 pr-4">
            <p className="text-caption-meta text-gray-600 mb-1 text-right">H2 / AR → 28px, SemiBold, Line height 34px, RTL enabled</p>
            <h2 className="text-h2-ar">هذا عنوان تجريبي من المستوى الثاني</h2>
          </div>
          
          <div className="border-r-4 border-yellow-500 pr-4">
            <p className="text-caption-meta text-gray-600 mb-1 text-right">H3 / AR → 22px, SemiBold, Line height 28px, RTL enabled</p>
            <h3 className="text-h3-ar">هذا عنوان تجريبي من المستوى الثالث</h3>
          </div>
          
          <div className="border-r-4 border-gray-500 pr-4">
            <p className="text-caption-meta text-gray-600 mb-1 text-right">Body / AR → 16px, Regular, Line height 24px, RTL enabled</p>
            <p className="text-body-ar">هذا نص تجريبي يوضح نمط النص الأساسي. يجب أن يكون سهل القراءة ومناسب للمحتوى العربي مع المسافات المناسبة للقراءة المريحة.</p>
          </div>
          
          <div className="border-r-4 border-gray-500 pr-4">
            <p className="text-caption-meta text-gray-600 mb-1 text-right">Caption / AR → 12px, Regular, Line height 16px, RTL enabled</p>
            <p className="text-caption-ar">هذا نص توضيحي للتعليقات والمعلومات الإضافية</p>
          </div>
        </div>
      </div>
    </div>
  );
}