import { ReactNode } from 'react';

interface RotaryButtonProps {
  variant: 'primary' | 'secondary';
  children: ReactNode;
  onClick?: () => void;
  className?: string;
}

export function RotaryButton({ variant, children, onClick, className = '' }: RotaryButtonProps) {
  const baseClasses = "inline-flex items-center justify-center px-4 py-3 rounded-md transition-colors duration-200 font-medium";
  
  const variantClasses = {
    primary: "bg-[#17458F] text-white hover:bg-[#0f3472] focus:ring-2 focus:ring-[#17458F] focus:ring-offset-2",
    secondary: "border-2 border-[#F7A81B] bg-white text-[#F7A81B] hover:bg-[#F7A81B] hover:text-white focus:ring-2 focus:ring-[#F7A81B] focus:ring-offset-2"
  };

  return (
    <button
      onClick={onClick}
      className={`${baseClasses} ${variantClasses[variant]} ${className}`}
    >
      {children}
    </button>
  );
}