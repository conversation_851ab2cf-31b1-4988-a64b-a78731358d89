import { RotaryButton } from './RotaryButton';
import { SectionHeader } from './SectionHeader';
import { EventCard } from './EventCard';
import { RotaryFooter } from './RotaryFooter';

export function ComponentShowcase() {
  return (
    <div className="mb-8">
      <h3 className="text-h3-desktop mb-6">Rotary Components</h3>
      
      {/* Buttons */}
      <div className="mb-8">
        <h4 className="text-h3-mobile mb-4 text-gray-700">Buttons</h4>
        <div className="flex flex-wrap gap-4">
          <RotaryButton variant="primary">
            Primary Button
          </RotaryButton>
          <RotaryButton variant="secondary">
            Secondary Button
          </RotaryButton>
        </div>
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <p className="text-body-small text-gray-600">
            <strong>Primary:</strong> Royal Blue fill (#17458F), White text, 6px radius, padding 12-16px<br/>
            <strong>Secondary:</strong> Gold border (#F7A81B), White fill, Gold text
          </p>
        </div>
      </div>

      {/* Section Header */}
      <div className="mb-8">
        <h4 className="text-h3-mobile mb-4 text-gray-700">Section Header</h4>
        <div className="border border-gray-200 rounded-lg p-4">
          <SectionHeader 
            title="Welcome to Our Rotary Club" 
            subtitle="Serving our community with dedication and purpose"
          />
        </div>
      </div>

      {/* Event Card */}
      <div className="mb-8">
        <h4 className="text-h3-mobile mb-4 text-gray-700">Event Card</h4>
        <div className="grid md:grid-cols-2 gap-4">
          <EventCard
            title="Weekly Club Meeting"
            date="Every Tuesday, 7:00 PM"
            location="Community Center, Main Street"
            description="Join us for our weekly meeting where we discuss ongoing projects and plan new community service initiatives."
          >
            <RotaryButton variant="primary" className="mt-3">
              RSVP Now
            </RotaryButton>
          </EventCard>
          
          <EventCard
            title="Food Drive Volunteer Day"
            date="Saturday, March 15th"
            location="Local Food Bank"
            description="Help us sort and distribute food to families in need. All volunteers welcome!"
          >
            <RotaryButton variant="secondary" className="mt-3">
              Sign Up
            </RotaryButton>
          </EventCard>
        </div>
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <p className="text-body-small text-gray-600">
            <strong>Event Card:</strong> White background, 1px neutral border, subtle shadow, 16px padding
          </p>
        </div>
      </div>

      {/* Footer Preview */}
      <div className="mb-8">
        <h4 className="text-h3-mobile mb-4 text-gray-700">Rotary Footer</h4>
        <div className="border border-gray-200 rounded-lg overflow-hidden">
          <RotaryFooter textColor="white">
            <div className="grid md:grid-cols-3 gap-6">
              <div>
                <h4 className="text-h3-mobile mb-3">About Rotary</h4>
                <p className="text-body-small">Rotary International is a global network of volunteer leaders.</p>
              </div>
              <div>
                <h4 className="text-h3-mobile mb-3">Quick Links</h4>
                <ul className="text-body-small space-y-1">
                  <li>Our Projects</li>
                  <li>Events</li>
                  <li>Membership</li>
                </ul>
              </div>
              <div>
                <h4 className="text-h3-mobile mb-3">Contact</h4>
                <p className="text-body-small"><EMAIL></p>
              </div>
            </div>
          </RotaryFooter>
        </div>
        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <p className="text-body-small text-gray-600">
            <strong>Rotary Footer:</strong> Royal Blue background (#17458F), Gold or White text options
          </p>
        </div>
      </div>
    </div>
  );
}