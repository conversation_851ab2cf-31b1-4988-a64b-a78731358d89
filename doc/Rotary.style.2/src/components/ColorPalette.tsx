interface ColorSwatch {
  name: string;
  hex: string;
  variable: string;
}

interface ColorPaletteProps {
  title: string;
  colors: ColorSwatch[];
}

export function ColorPalette({ title, colors }: ColorPaletteProps) {
  return (
    <div className="mb-8">
      <h3 className="text-h3-desktop mb-4">{title}</h3>
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
        {colors.map((color) => (
          <div key={color.name} className="flex flex-col items-center">
            <div
              className="w-20 h-20 rounded-lg border border-gray-200 mb-2 shadow-sm"
              style={{ backgroundColor: color.hex }}
            />
            <div className="text-center">
              <p className="text-body-small font-medium text-gray-900">{color.name}</p>
              <p className="text-caption-meta text-gray-600">{color.hex}</p>
              <p className="text-caption-meta text-gray-500 font-mono">{color.variable}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}