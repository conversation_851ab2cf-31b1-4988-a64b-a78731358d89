import { SectionHeader } from './SectionHeader';
import { RotaryNavigation } from './RotaryNavigation';
import { FeatureGrid, TwoColumnContent, Stats, CTASection, NewsGrid } from './ContentLayouts';

export function LayoutShowcase() {
  // Sample data for demonstrations
  const features = [
    {
      icon: '🌍',
      title: 'Global Reach',
      description: 'Connect with 1.4 million members across 200+ countries and geographic areas.',
      link: '#'
    },
    {
      icon: '🤝',
      title: 'Service Projects',
      description: 'Make a lasting impact through community service and humanitarian projects.',
      link: '#'
    },
    {
      icon: '👥',
      title: 'Networking',
      description: 'Build professional relationships and expand your network globally.',
      link: '#'
    },
    {
      icon: '🎓',
      title: 'Leadership',
      description: 'Develop leadership skills through hands-on experience and training.',
      link: '#'
    },
    {
      icon: '💡',
      title: 'Innovation',
      description: 'Drive positive change through innovative solutions and partnerships.',
      link: '#'
    },
    {
      icon: '🏆',
      title: 'Recognition',
      description: 'Celebrate achievements and recognize outstanding service contributions.',
      link: '#'
    }
  ];

  const stats = [
    { value: '1.4M+', label: 'Members Worldwide', description: 'Active Rotarians globally' },
    { value: '46,000+', label: 'Clubs', description: 'Local Rotary clubs' },
    { value: '200+', label: 'Countries & Areas', description: 'Global presence' },
    { value: '$3.7B', label: 'Donated Annually', description: 'In funding and volunteer time' }
  ];

  const newsItems = [
    {
      title: 'Rotary Foundation Reaches $500 Million Fundraising Milestone',
      excerpt: 'The Rotary Foundation announces record-breaking fundraising success, enabling expanded global humanitarian efforts.',
      date: 'March 15, 2024',
      category: 'Foundation',
      image: 'https://images.unsplash.com/photo-1727473704320-71a9ac47cfdd?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=***********************************************************************************************&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
      link: '#'
    },
    {
      title: 'Global Polio Eradication Initiative Shows Continued Progress',
      excerpt: 'Latest reports show significant advancement in the worldwide effort to eliminate polio completely.',
      date: 'March 10, 2024',
      category: 'Health',
      image: 'https://images.unsplash.com/photo-**********-1985ebfe28b1?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=*******************************************************************************************************************&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
      link: '#'
    },
    {
      title: 'Youth Exchange Program Celebrates 50 Years of Cultural Understanding',
      excerpt: 'Rotary Youth Exchange marks its golden anniversary, having facilitated thousands of cultural exchanges.',
      date: 'March 5, 2024',
      category: 'Youth',
      image: 'https://images.unsplash.com/photo-**********-e580a9adbf43?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=**********************************************************************************************************&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral',
      link: '#'
    }
  ];

  return (
    <div className="space-y-12">
      <SectionHeader 
        title="Professional Layout Components"
        subtitle="Comprehensive content layout components designed for organizational websites and digital communications"
      />
      
      {/* Navigation Component */}
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-h3-desktop text-[#17458F] mb-2">Navigation Component</h3>
          <p className="text-body-default text-gray-600 mb-4">
            Professional navigation bar with dropdown menus, mobile responsiveness, and consistent Rotary branding.
          </p>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-body-default font-medium mb-2">Features:</h4>
            <ul className="text-body-small text-gray-600 space-y-1">
              <li>• Responsive dropdown navigation with hover states</li>
              <li>• Mobile-first design with hamburger menu</li>
              <li>• Primary and secondary color variants</li>
              <li>• Customizable CTA button integration</li>
              <li>• Accessibility-compliant structure</li>
            </ul>
          </div>
        </div>
        
        {/* Primary Navigation Demo */}
        <div className="bg-gray-100 rounded-lg overflow-hidden">
          <RotaryNavigation variant="primary" />
        </div>
        
        {/* Secondary Navigation Demo */}
        <div className="bg-gray-100 rounded-lg overflow-hidden">
          <RotaryNavigation variant="secondary" ctaText="Donate Now" ctaVariant="primary" />
        </div>
      </div>

      {/* Feature Grid Component */}
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-h3-desktop text-[#17458F] mb-2">Feature Grid</h3>
          <p className="text-body-default text-gray-600 mb-4">
            Flexible grid layout for showcasing features, services, or key benefits with icons and descriptions.
          </p>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-body-default font-medium mb-2">Customization Options:</h4>
            <ul className="text-body-small text-gray-600 space-y-1">
              <li>• Configurable column layouts (2, 3, or 4 columns)</li>
              <li>• Icon integration with hover effects</li>
              <li>• Optional linking for each feature</li>
              <li>• Responsive grid system</li>
              <li>• Consistent Rotary color scheme</li>
            </ul>
          </div>
        </div>
        
        <div className="bg-gray-100 rounded-lg overflow-hidden">
          <FeatureGrid
            title="Why Join Rotary?"
            subtitle="Discover the benefits of joining a global network of service-minded professionals"
            features={features}
            columns={3}
          />
        </div>
      </div>

      {/* Two Column Content Component */}
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-h3-desktop text-[#17458F] mb-2">Two Column Content</h3>
          <p className="text-body-default text-gray-600 mb-4">
            Versatile content layout combining text with images, perfect for storytelling and detailed explanations.
          </p>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-body-default font-medium mb-2">Layout Features:</h4>
            <ul className="text-body-small text-gray-600 space-y-1">
              <li>• Left or right image positioning</li>
              <li>• Multiple background color options</li>
              <li>• Integrated call-to-action buttons</li>
              <li>• Responsive image handling</li>
              <li>• Multi-paragraph content support</li>
            </ul>
          </div>
        </div>
        
        <div className="bg-gray-100 rounded-lg overflow-hidden">
          <TwoColumnContent
            title="Making a Global Impact"
            content="Rotary International has been working to improve communities around the world for over 115 years. Our members are people of action who tackle some of the world's most pressing humanitarian challenges.

Through our global network of volunteers, we connect communities and countries to create lasting change in the areas of peace, health, education, economic development, and environmental sustainability."
            imageSrc="https://images.unsplash.com/photo-**********-1985ebfe28b1?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=*******************************************************************************************************************&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral"
            imageAlt="Volunteers helping in community service"
            imagePosition="right"
            ctaText="Learn About Our Work"
            ctaLink="#"
            backgroundColor="blue"
          />
        </div>
      </div>

      {/* Stats Component */}
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-h3-desktop text-[#17458F] mb-2">Statistics Display</h3>
          <p className="text-body-default text-gray-600 mb-4">
            Impactful statistics presentation with multiple background themes and responsive layout.
          </p>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-body-default font-medium mb-2">Visual Options:</h4>
            <ul className="text-body-small text-gray-600 space-y-1">
              <li>• Blue, white, and gold background variants</li>
              <li>• Large number emphasis with Gold accents</li>
              <li>• Optional descriptions for context</li>
              <li>• Grid layout adapts to content quantity</li>
              <li>• High contrast for readability</li>
            </ul>
          </div>
        </div>
        
        <div className="bg-gray-100 rounded-lg overflow-hidden">
          <Stats
            title="Our Global Impact"
            subtitle="See how Rotary's network creates positive change worldwide"
            stats={stats}
            backgroundColor="blue"
          />
        </div>
      </div>

      {/* CTA Section Component */}
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-h3-desktop text-[#17458F] mb-2">Call-to-Action Section</h3>
          <p className="text-body-default text-gray-600 mb-4">
            Compelling action sections with background images and strong visual appeal for conversions.
          </p>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-body-default font-medium mb-2">Engagement Features:</h4>
            <ul className="text-body-small text-gray-600 space-y-1">
              <li>• Background image support with overlay options</li>
              <li>• Primary and secondary CTA buttons</li>
              <li>• Centered content layout for focus</li>
              <li>• High contrast text for readability</li>
              <li>• Mobile-optimized button layouts</li>
            </ul>
          </div>
        </div>
        
        <div className="bg-gray-100 rounded-lg overflow-hidden">
          <CTASection
            title="Ready to Make a Difference?"
            subtitle="Join thousands of Rotarians worldwide who are creating positive change in their communities and beyond."
            primaryCTA="Find a Club Near You"
            secondaryCTA="Start Your Own Club"
            backgroundImage="https://images.unsplash.com/photo-**********-e580a9adbf43?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=**********************************************************************************************************&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral"
            overlay={true}
          />
        </div>
      </div>

      {/* News Grid Component */}
      <div className="space-y-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-h3-desktop text-[#17458F] mb-2">News & Content Grid</h3>
          <p className="text-body-default text-gray-600 mb-4">
            Professional content grid for news, blog posts, or featured content with category organization.
          </p>
          
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-body-default font-medium mb-2">Content Management:</h4>
            <ul className="text-body-small text-gray-600 space-y-1">
              <li>• Card-based layout with hover effects</li>
              <li>• Category and date metadata display</li>
              <li>• Image integration with fallback support</li>
              <li>• Excerpt and read-more functionality</li>
              <li>• Configurable display quantity with "View All" option</li>
            </ul>
          </div>
        </div>
        
        <div className="bg-gray-100 rounded-lg overflow-hidden">
          <NewsGrid
            title="Latest News & Updates"
            subtitle="Stay informed about Rotary's global initiatives and community impact"
            items={newsItems}
            showAll={false}
          />
        </div>
      </div>

      {/* Implementation Guidelines */}
      <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
        <h3 className="text-h3-desktop text-[#17458F] mb-4">Implementation Guidelines</h3>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-body-default font-medium mb-2 text-[#F7A81B]">Best Practices:</h4>
            <ul className="text-body-small text-gray-700 space-y-1">
              <li>• Use consistent spacing between layout sections</li>
              <li>• Maintain proper content hierarchy with headers</li>
              <li>• Ensure mobile responsiveness across all layouts</li>
              <li>• Test image loading and provide appropriate fallbacks</li>
              <li>• Implement proper semantic HTML for accessibility</li>
            </ul>
          </div>
          <div>
            <h4 className="text-body-default font-medium mb-2 text-[#F7A81B]">Content Strategy:</h4>
            <ul className="text-body-small text-gray-700 space-y-1">
              <li>• Keep feature descriptions concise and action-oriented</li>
              <li>• Use high-quality, relevant images throughout</li>
              <li>• Maintain consistent tone and voice across sections</li>
              <li>• Include clear calls-to-action in appropriate contexts</li>
              <li>• Update statistics and news content regularly</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}