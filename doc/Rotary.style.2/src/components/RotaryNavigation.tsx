import { useState } from 'react';
import { RotaryButton } from './RotaryButton';

interface NavigationItem {
  label: string;
  href: string;
  children?: NavigationItem[];
}

interface RotaryNavigationProps {
  variant?: 'primary' | 'secondary';
  showCTA?: boolean;
  ctaText?: string;
  ctaVariant?: 'primary' | 'secondary';
}

export function RotaryNavigation({ 
  variant = 'primary', 
  showCTA = true,
  ctaText = "Join Rotary",
  ctaVariant = 'primary'
}: RotaryNavigationProps) {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null);

  const navigationItems: NavigationItem[] = [
    {
      label: 'About Rotary',
      href: '/about',
      children: [
        { label: 'Our History', href: '/about/history' },
        { label: 'Leadership', href: '/about/leadership' },
        { label: 'Our Foundation', href: '/about/foundation' },
        { label: 'Global Impact', href: '/about/impact' }
      ]
    },
    {
      label: 'Get Involved',
      href: '/get-involved',
      children: [
        { label: 'Join a Club', href: '/join' },
        { label: 'Start a Club', href: '/start-club' },
        { label: 'Volunteer', href: '/volunteer' },
        { label: 'Donate', href: '/donate' }
      ]
    },
    {
      label: 'Our Work',
      href: '/our-work',
      children: [
        { label: 'Areas of Focus', href: '/our-work/areas' },
        { label: 'Projects', href: '/our-work/projects' },
        { label: 'Grants', href: '/our-work/grants' },
        { label: 'Success Stories', href: '/our-work/stories' }
      ]
    },
    {
      label: 'News & Events',
      href: '/news',
      children: [
        { label: 'Latest News', href: '/news/latest' },
        { label: 'Events', href: '/events' },
        { label: 'Calendar', href: '/calendar' },
        { label: 'Media Resources', href: '/media' }
      ]
    },
    {
      label: 'Members',
      href: '/members'
    }
  ];

  const bgColor = variant === 'primary' ? 'bg-white' : 'bg-[#17458F]';
  const textColor = variant === 'primary' ? 'text-[#17458F]' : 'text-white';
  const hoverColor = variant === 'primary' ? 'hover:text-[#F7A81B]' : 'hover:text-[#F7A81B]';

  return (
    <nav className={`${bgColor} shadow-md relative z-50`}>
      <div className="max-w-7xl mx-auto px-6">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className={`text-h2-desktop ${textColor} font-bold`}>
              Rotary
            </div>
            <div className="hidden sm:block ml-2 w-px h-6 bg-gray-300" />
            <div className={`hidden sm:block ml-2 text-body-small ${textColor} opacity-75`}>
              Service Above Self
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-8">
            {navigationItems.map((item) => (
              <div
                key={item.label}
                className="relative"
                onMouseEnter={() => setActiveDropdown(item.label)}
                onMouseLeave={() => setActiveDropdown(null)}
              >
                <a
                  href={item.href}
                  className={`${textColor} ${hoverColor} text-body-default font-medium transition-colors duration-200 py-2`}
                >
                  {item.label}
                </a>
                
                {/* Dropdown Menu */}
                {item.children && activeDropdown === item.label && (
                  <div className="absolute top-full left-0 mt-1 w-56 bg-white rounded-lg shadow-lg border border-gray-200 py-2">
                    {item.children.map((child) => (
                      <a
                        key={child.label}
                        href={child.href}
                        className="block px-4 py-2 text-body-small text-[#17458F] hover:text-[#F7A81B] hover:bg-gray-50 transition-colors"
                      >
                        {child.label}
                      </a>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>

          {/* CTA Button */}
          {showCTA && (
            <div className="hidden lg:block">
              <RotaryButton variant={ctaVariant} className="px-6 py-2">
                {ctaText}
              </RotaryButton>
            </div>
          )}

          {/* Mobile Menu Button */}
          <div className="lg:hidden">
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className={`${textColor} p-2`}
              aria-label="Toggle mobile menu"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {mobileMenuOpen ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                )}
              </svg>
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <div className="lg:hidden border-t border-gray-200 py-4">
            {navigationItems.map((item) => (
              <div key={item.label} className="py-2">
                <a
                  href={item.href}
                  className={`block ${textColor} ${hoverColor} text-body-default font-medium py-2`}
                >
                  {item.label}
                </a>
                {item.children && (
                  <div className="ml-4 mt-2 space-y-2">
                    {item.children.map((child) => (
                      <a
                        key={child.label}
                        href={child.href}
                        className={`block ${textColor} opacity-75 text-body-small py-1 hover:opacity-100`}
                      >
                        {child.label}
                      </a>
                    ))}
                  </div>
                )}
              </div>
            ))}
            {showCTA && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <RotaryButton variant={ctaVariant} className="w-full">
                  {ctaText}
                </RotaryButton>
              </div>
            )}
          </div>
        )}
      </div>
    </nav>
  );
}