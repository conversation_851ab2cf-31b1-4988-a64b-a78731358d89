import { RotaryButton } from './RotaryButton';
import { ImageWithFallback } from './figma/ImageWithFallback';

interface HeroPageTemplateProps {
  variant?: 'service' | 'business' | 'fundraising';
  title?: string;
  subtitle?: string;
  description?: string;
  primaryCTA?: string;
  secondaryCTA?: string;
  backgroundImage?: string;
  overlayOpacity?: number;
  textAlignment?: 'left' | 'center' | 'right';
  showStats?: boolean;
  stats?: Array<{ value: string; label: string }>;
}

export function HeroPageTemplate({
  variant = 'service',
  title = "Service Above Self",
  subtitle = "Join Rotary International",
  description = "Connect with a global network of passionate individuals working together to create positive change in communities around the world.",
  primaryCTA = "Join Our Club",
  secondaryCTA = "Learn More",
  backgroundImage,
  overlayOpacity = 0.4,
  textAlignment = 'center',
  showStats = false,
  stats = [
    { value: "1.4M+", label: "Members Worldwide" },
    { value: "46,000+", label: "Clubs Globally" },
    { value: "200+", label: "Countries & Areas" }
  ]
}: HeroPageTemplateProps) {

  const getVariantData = () => {
    switch (variant) {
      case 'service':
        return {
          bgImage: "https://images.unsplash.com/photo-1560220604-1985ebfe28b1?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxyb3RhcnklMjBzZXJ2aWNlJTIwY29tbXVuaXR5JTIwdm9sdW50ZWVyc3xlbnwxfHx8fDE3NTY5OTEwMjR8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral",
          title: "Service Above Self",
          subtitle: "Join Rotary International",
          description: "Connect with a global network of passionate individuals working together to create positive change in communities around the world."
        };
      case 'business':
        return {
          bgImage: "https://images.unsplash.com/photo-1557804500-7a58fbcd4d1a?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxidXNpbmVzcyUyMGNvbmZlcmVuY2UlMjBtZWV0aW5nfGVufDF8fHx8MTc1Njk5MTAyOHww&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral",
          title: "Professional Excellence",
          subtitle: "Rotary District Conference 2024",
          description: "Join business leaders and professionals for networking, education, and inspiration at our annual district conference."
        };
      case 'fundraising':
        return {
          bgImage: "https://images.unsplash.com/photo-1724167924357-2d5febf3f931?crop=entropy&cs=tinysrgb&fit=max&fm=jpg&ixid=M3w3Nzg4Nzd8MHwxfHNlYXJjaHwxfHxjaGFyaXR5JTIwZnVuZHJhaXNpbmclMjBldmVudHxlbnwxfHx8fDE3NTY4OTkxMTR8MA&ixlib=rb-4.1.0&q=80&w=1080&utm_source=figma&utm_medium=referral",
          title: "Make a Difference",
          subtitle: "Annual Charity Gala",
          description: "Help us raise funds for critical community projects and humanitarian initiatives that change lives."
        };
      default:
        return { bgImage: backgroundImage, title, subtitle, description };
    }
  };

  const variantData = getVariantData();
  const finalBgImage = backgroundImage || variantData.bgImage;
  const finalTitle = title === "Service Above Self" ? variantData.title : title;
  const finalSubtitle = subtitle === "Join Rotary International" ? variantData.subtitle : subtitle;
  const finalDescription = description === "Connect with a global network of passionate individuals working together to create positive change in communities around the world." ? variantData.description : description;

  const alignmentClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  };

  return (
    <div className="relative min-h-screen flex flex-col">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <ImageWithFallback
          src={finalBgImage}
          alt="Hero background"
          className="w-full h-full object-cover"
        />
        <div 
          className="absolute inset-0 bg-[#17458F]"
          style={{ opacity: overlayOpacity }}
        />
      </div>

      {/* Content */}
      <div className="relative z-10 flex-1 flex flex-col">
        {/* Header/Navigation Bar */}
        <header className="py-6 px-6 lg:px-12">
          <div className="max-w-7xl mx-auto flex justify-between items-center">
            <div className="text-h2-desktop text-white">
              Rotary
            </div>
            <nav className="hidden md:flex space-x-8">
              <a href="#" className="text-body-default text-white hover:text-[#F7A81B] transition-colors">About</a>
              <a href="#" className="text-body-default text-white hover:text-[#F7A81B] transition-colors">Events</a>
              <a href="#" className="text-body-default text-white hover:text-[#F7A81B] transition-colors">Projects</a>
              <a href="#" className="text-body-default text-white hover:text-[#F7A81B] transition-colors">Join</a>
            </nav>
          </div>
        </header>

        {/* Hero Content */}
        <main className="flex-1 flex items-center px-6 lg:px-12">
          <div className="max-w-7xl mx-auto w-full">
            <div className="max-w-4xl mx-auto">
              <div className={alignmentClasses[textAlignment]}>
                {/* Subtitle */}
                <div className="inline-block bg-[#F7A81B] text-[#17458F] px-4 py-2 rounded-full mb-6">
                  <span className="text-body-small font-medium">{finalSubtitle}</span>
                </div>

                {/* Main Title */}
                <h1 className="text-h1-desktop lg:text-[48px] lg:leading-[56px] text-white mb-6">
                  {finalTitle}
                </h1>

                {/* Description */}
                <p className="text-body-default lg:text-[18px] lg:leading-[28px] text-white opacity-90 mb-8 max-w-2xl mx-auto">
                  {finalDescription}
                </p>

                {/* CTA Buttons */}
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <RotaryButton variant="primary" className="px-8 py-4 text-[#17458F] bg-white hover:bg-gray-100">
                    {primaryCTA}
                  </RotaryButton>
                  <RotaryButton variant="secondary" className="px-8 py-4 text-white border-white hover:bg-white hover:text-[#17458F]">
                    {secondaryCTA}
                  </RotaryButton>
                </div>
              </div>
            </div>
          </div>
        </main>

        {/* Stats Section */}
        {showStats && (
          <div className="py-12 px-6 lg:px-12">
            <div className="max-w-7xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                {stats.map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-h1-desktop text-[#F7A81B] mb-2">
                      {stat.value}
                    </div>
                    <div className="text-body-default text-white opacity-90">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Scroll Indicator */}
        <div className="pb-8 text-center">
          <div className="inline-flex flex-col items-center text-white opacity-75">
            <span className="text-caption-meta mb-2">Scroll to explore</span>
            <div className="w-px h-8 bg-white animate-pulse"></div>
          </div>
        </div>
      </div>
    </div>
  );
}