import { SectionHeader } from './SectionHeader';

export function QuickReference() {
  const categories = [
    {
      title: "Color Tokens",
      items: [
        { name: "Primary Royal Blue", token: "--rotary-primary-royal-blue", value: "#17458F" },
        { name: "Primary Gold", token: "--rotary-primary-gold", value: "#F7A81B" },
        { name: "Secondary Azure", token: "--rotary-secondary-azure", value: "#0077C8" },
        { name: "Secondary Sky Blue", token: "--rotary-secondary-sky-blue", value: "#5BC2E7" }
      ]
    },
    {
      title: "Typography Classes",
      items: [
        { name: "Desktop H1", token: ".text-h1-desktop", value: "36px/44px, 700 weight" },
        { name: "Desktop H2", token: ".text-h2-desktop", value: "28px/34px, 600 weight" },
        { name: "Body Default", token: ".text-body-default", value: "16px/24px, 400 weight" },
        { name: "Caption Meta", token: ".text-caption-meta", value: "12px/16px, 400 weight" }
      ]
    },
    {
      title: "Component Library",
      items: [
        { name: "Rotary Button", token: "<RotaryButton>", value: "Primary & secondary variants" },
        { name: "Section Header", token: "<SectionHeader>", value: "Title with optional subtitle" },
        { name: "Event Card", token: "<EventCard>", value: "Date, title, description layout" },
        { name: "Rotary Footer", token: "<RotaryFooter>", value: "Branded footer with links" }
      ]
    },
    {
      title: "Layout Patterns",
      items: [
        { name: "Navigation", token: "<RotaryNavigation>", value: "Responsive nav with dropdowns" },
        { name: "Feature Grid", token: "<FeatureGrid>", value: "2-4 column responsive grid" },
        { name: "Two Column", token: "<TwoColumnContent>", value: "Image + text layouts" },
        { name: "Stats Display", token: "<Stats>", value: "Number highlights with context" }
      ]
    },
    {
      title: "Templates",
      items: [
        { name: "Hero Pages", token: "<HeroPageTemplate>", value: "Service, business, fundraising" },
        { name: "Email Templates", token: "<RotaryEmailTemplate>", value: "Event announcements" },
        { name: "CTA Sections", token: "<CTASection>", value: "Call-to-action with background" },
        { name: "News Grid", token: "<NewsGrid>", value: "Article cards with metadata" }
      ]
    }
  ];

  return (
    <div className="space-y-8">
      <SectionHeader 
        title="Quick Reference Guide"
        subtitle="Essential tokens, classes, and components for rapid development"
      />
      
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.map((category, index) => (
          <div key={index} className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="bg-[#17458F] px-4 py-3">
              <h3 className="text-h3-mobile text-white">{category.title}</h3>
            </div>
            <div className="p-4 space-y-3">
              {category.items.map((item, itemIndex) => (
                <div key={itemIndex} className="border-b border-gray-100 pb-2 last:border-b-0">
                  <div className="text-body-small font-medium text-[#17458F] mb-1">
                    {item.name}
                  </div>
                  <div className="text-caption-meta text-gray-600 font-mono bg-gray-50 px-2 py-1 rounded">
                    {item.token}
                  </div>
                  <div className="text-caption-meta text-gray-500 mt-1">
                    {item.value}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Usage Examples */}
      <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
        <h3 className="text-h3-desktop text-[#17458F] mb-4">Usage Examples</h3>
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-body-default font-medium mb-2 text-[#F7A81B]">CSS Custom Properties:</h4>
            <div className="bg-white p-3 rounded border font-mono text-caption-meta">
              <code>
                background-color: var(--rotary-primary-royal-blue);<br/>
                color: var(--rotary-primary-gold);
              </code>
            </div>
          </div>
          <div>
            <h4 className="text-body-default font-medium mb-2 text-[#F7A81B]">Tailwind Classes:</h4>
            <div className="bg-white p-3 rounded border font-mono text-caption-meta">
              <code>
                {'className="bg-[#17458F] text-[#F7A81B]"'}<br/>
                {'className="text-h2-desktop"'}
              </code>
            </div>
          </div>
          <div>
            <h4 className="text-body-default font-medium mb-2 text-[#F7A81B]">Component Import:</h4>
            <div className="bg-white p-3 rounded border font-mono text-caption-meta">
              <code>
                import {'{ RotaryButton }'} from './components/RotaryButton';<br/>
                {'<RotaryButton variant="primary">'}
              </code>
            </div>
          </div>
          <div>
            <h4 className="text-body-default font-medium mb-2 text-[#F7A81B]">Layout Usage:</h4>
            <div className="bg-white p-3 rounded border font-mono text-caption-meta">
              <code>
                {'<FeatureGrid columns={3} features={data} />'}<br/>
                {'<TwoColumnContent imagePosition="left" />'}
              </code>
            </div>
          </div>
        </div>
      </div>

      {/* Development Workflow */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <h3 className="text-h3-desktop text-[#17458F] mb-4">Development Workflow</h3>
        <div className="grid md:grid-cols-4 gap-4">
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-[#17458F] text-white rounded-full flex items-center justify-center mx-auto mb-3 text-h3-mobile">
              1
            </div>
            <h4 className="text-body-default font-medium mb-2">Choose Colors</h4>
            <p className="text-body-small text-gray-600">Select from primary, secondary, or neutral palette</p>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-[#17458F] text-white rounded-full flex items-center justify-center mx-auto mb-3 text-h3-mobile">
              2
            </div>
            <h4 className="text-body-default font-medium mb-2">Apply Typography</h4>
            <p className="text-body-small text-gray-600">Use established heading and body text classes</p>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-[#17458F] text-white rounded-full flex items-center justify-center mx-auto mb-3 text-h3-mobile">
              3
            </div>
            <h4 className="text-body-default font-medium mb-2">Build Layout</h4>
            <p className="text-body-small text-gray-600">Combine layout components and UI elements</p>
          </div>
          <div className="text-center p-4 bg-gray-50 rounded-lg">
            <div className="w-12 h-12 bg-[#17458F] text-white rounded-full flex items-center justify-center mx-auto mb-3 text-h3-mobile">
              4
            </div>
            <h4 className="text-body-default font-medium mb-2">Test & Deploy</h4>
            <p className="text-body-small text-gray-600">Verify responsiveness and accessibility</p>
          </div>
        </div>
      </div>
    </div>
  );
}