import imgImage940 from "figma:asset/9d167e8d54dd630ef0449d85800681a387777f6b.png";
import imgEllipse1866 from "figma:asset/272a1f8f28c77039953c162f97bf31bb4a317c1c.png";
import imgEllipse1867 from "figma:asset/c06bf8e90c1ecc5cad21fc543d009902a39ba6f9.png";
import imgEllipse1868 from "figma:asset/39c8601cb5f77d0e8fa17f120da453751ac5a830.png";
import { imgGroup, imgGroup1, imgGroup2, imgGroup3, imgGroup4, imgGroup5, imgGroup6, imgGroup7, imgLine75, imgLine76 } from "../imports/svg-cuw8t";
import { RotaryButton } from './RotaryButton';

interface EmailTemplateProps {
  eventTitle?: string;
  eventDate?: string;
  eventTime?: string;
  eventDescription?: string;
  logoText?: string;
  headerSubtitle?: string;
}

export function RotaryEmailTemplate({
  eventTitle = "Streamline Your Success with Our Live Webinar: Digital Marketing Breakthroughs!",
  eventDate = "August 12th, 2023",
  eventTime = "1:00PM PST",
  eventDescription = "Harness the power of innovative marketing techniques to revolutionize your brand's online presence.",
  logoText = "Rotary Club",
  headerSubtitle = "Learn the latest strategies from the experts and elevate your digital marketing game!"
}: EmailTemplateProps) {
  
  return (
    <div className="bg-white relative w-full max-w-[600px] mx-auto">
      {/* Header */}
      <div className="bg-[#17458F] h-[66px] w-full flex items-center justify-center relative">
        <div className="text-h2-desktop text-white">{logoText}</div>
      </div>
      
      {/* Header Gradient */}
      <div className="bg-gradient-to-b from-[#17458F] to-[#0f3472] h-[25px] w-full" />
      
      {/* Header Subtitle */}
      <div className="bg-[#fcfcfc] px-[40px] py-4">
        <p className="text-body-small text-[#17458F] text-center">{headerSubtitle}</p>
      </div>

      {/* Hero Image */}
      <div className="flex justify-center px-12 py-4">
        <div 
          className="bg-center bg-cover bg-no-repeat h-[322px] w-[504px] rounded-lg"
          style={{ backgroundImage: `url('${imgImage940}')` }} 
        />
      </div>

      {/* Event Title Section */}
      <div className="bg-[#17458F] bg-opacity-15 py-6 px-12">
        <h1 className="text-h2-desktop text-[#17458F] text-center leading-relaxed">
          {eventTitle}
        </h1>
      </div>

      {/* Event Description */}
      <div className="px-12 py-6">
        <p className="text-body-default text-[#282828] text-center max-w-[422px] mx-auto">
          {eventDescription}
        </p>
      </div>

      {/* CTA Button */}
      <div className="flex justify-center pb-6">
        <RotaryButton variant="primary" className="px-8 py-3">
          Secure My Spot
        </RotaryButton>
      </div>

      {/* Event Details */}
      <div className="bg-[#fcfcfc] py-6">
        <div className="flex items-center justify-center">
          <div className="flex items-center gap-8">
            <div className="flex items-center gap-2">
              <div className="w-[46px] h-[43px] flex items-center justify-center">
                <img src={imgGroup} alt="Calendar" className="w-full h-full" />
              </div>
              <div>
                <p className="text-body-small text-[#282828] font-medium">DATE:</p>
                <p className="text-body-small text-[#282828]">{eventDate}</p>
              </div>
            </div>
            
            <div className="h-[34px] w-px bg-gray-300" />
            
            <div className="flex items-center gap-2">
              <div className="w-[43px] h-[43px] flex items-center justify-center">
                <img src={imgGroup1} alt="Clock" className="w-full h-full" />
              </div>
              <div>
                <p className="text-body-small text-[#282828] font-medium">TIME:</p>
                <p className="text-body-small text-[#282828]">{eventTime}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Inside the Webinar Section */}
      <div className="px-12 py-8">
        <h2 className="text-h2-desktop text-[#17458F] mb-4">Inside the Webinar</h2>
        <p className="text-body-default text-[#282828] mb-6">
          Unlock the potential of digital marketing and stay ahead of the curve with our insider knowledge and strategy showcase.
        </p>
        
        <p className="text-body-default text-[#F7A81B] mb-6">What You'll Discover:</p>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-[35px] h-[35px] mx-auto mb-3 flex items-center justify-center">
              <img src={imgGroup2} alt="Innovation" className="w-full h-full" />
            </div>
            <p className="text-body-small text-[#282828]">
              The latest trends reshaping the digital landscape and how to leverage them.
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-[35px] h-[35px] mx-auto mb-3 flex items-center justify-center">
              <img src={imgGroup3} alt="Tools" className="w-full h-full" />
            </div>
            <p className="text-body-small text-[#282828]">
              Cutting-edge tools and platforms that will enhance your marketing efficiency.
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-[35px] h-[35px] mx-auto mb-3 flex items-center justify-center">
              <img src={imgGroup4} alt="Networking" className="w-full h-full" />
            </div>
            <p className="text-body-small text-[#282828]">
              Networking strategies to connect with influencers and amplify your reach.
            </p>
          </div>
        </div>
      </div>

      {/* Speakers Section */}
      <div className="px-12 py-8">
        <h2 className="text-h2-desktop text-[#17458F] mb-8">Speakers</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-[77px] h-[77px] mx-auto mb-4">
              <img src={imgEllipse1866} alt="Julianne Grant" className="w-full h-full rounded-full" />
            </div>
            <div className="h-[48px] w-px bg-gray-300 mx-auto mb-3" />
            <h3 className="text-body-default text-black mb-1">Julianne Grant</h3>
            <p className="text-caption-meta text-[#F7A81B] mb-3">Digital Marketing Strategist</p>
            <p className="text-caption-meta text-[#282828]">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-[77px] h-[77px] mx-auto mb-4">
              <img src={imgEllipse1867} alt="Lucas Werner" className="w-full h-full rounded-full" />
            </div>
            <div className="h-[48px] w-px bg-gray-300 mx-auto mb-3" />
            <h3 className="text-body-default text-black mb-1">Lucas Werner</h3>
            <p className="text-caption-meta text-[#F7A81B] mb-3">Branding Expert</p>
            <p className="text-caption-meta text-[#282828]">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-[77px] h-[77px] mx-auto mb-4">
              <img src={imgEllipse1868} alt="Nadia Patel" className="w-full h-full rounded-full" />
            </div>
            <div className="h-[48px] w-px bg-gray-300 mx-auto mb-3" />
            <h3 className="text-body-default text-black mb-1">Nadia Patel</h3>
            <p className="text-caption-meta text-[#F7A81B] mb-3">SEO & Analytics Guru</p>
            <p className="text-caption-meta text-[#282828]">
              Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod
            </p>
          </div>
        </div>
      </div>

      {/* RSVP Section */}
      <div className="bg-[#fcfcfc] py-8">
        <div className="text-center">
          <h2 className="text-h2-mobile text-[#17458F] mb-6">Will you join us?</h2>
          <div className="flex justify-center gap-4">
            <RotaryButton variant="primary" className="px-6 py-2">
              Yes
            </RotaryButton>
            <RotaryButton variant="secondary" className="px-6 py-2">
              No
            </RotaryButton>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="bg-[#0f3472] py-8 px-12">
        <div className="flex justify-center gap-6 mb-4">
          <div className="w-[23px] h-[23px] bg-white rounded-full flex items-center justify-center">
            <img src={imgGroup5} alt="Facebook" className="w-full h-full" />
          </div>
          <div className="w-[23px] h-[23px] bg-white rounded-full flex items-center justify-center">
            <img src={imgGroup6} alt="Twitter" className="w-full h-full" />
          </div>
          <div className="w-[23px] h-[23px] bg-white rounded-full flex items-center justify-center">
            <img src={imgGroup7} alt="LinkedIn" className="w-full h-full" />
          </div>
        </div>
        
        <div className="text-center text-white">
          <p className="text-body-small mb-2">
            Home | Support | Our Policy | Member Area
          </p>
          <p className="text-body-small mb-4">
            You're receiving this email because you signed up for our webinar alerts.
          </p>
          <div className="flex justify-center gap-6">
            <a href="#" className="text-caption-meta text-white underline">Manage Preferences</a>
            <a href="#" className="text-caption-meta text-white underline">Unsubscribe</a>
          </div>
        </div>
      </div>
    </div>
  );
}