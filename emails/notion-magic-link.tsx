import React from 'react';
import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Text,
} from '@react-email/components';

interface NotionMagicLinkEmailProps {
  loginCode?: string;
}

export const NotionMagicLinkEmail = ({
  loginCode,
}: NotionMagicLinkEmailProps) => (
  <Html>
    <Head />
    <Preview>Log in with this magic link</Preview>
    <Body style={main}>
      <Container style={container}>
        <Heading style={h1}>Login</Heading>
        <Link
          href="https://notion.so"
          target="_blank"
          style={{
            ...link,
            display: 'block',
            marginBottom: '16px',
          }}
        >
          Click here to log in with this magic link
        </Link>
        <Text style={{ ...text, marginBottom: '14px' }}>
          Or, copy and paste this temporary login code:
        </Text>
        <code style={code}>{loginCode}</code>
        <Text
          style={{
            ...text,
            color: '#ababab',
            marginTop: '14px',
            marginBottom: '16px',
          }}
        >
          If you didn&apos;t try to login, you can safely ignore this email.
        </Text>
        <Text
          style={{
            ...text,
            color: '#ababab',
            marginTop: '12px',
            marginBottom: '38px',
          }}
        >
          Hint: You can set a permanent password in Settings & members → My
          account.
        </Text>
        <Img
          src="/static/Rotary_logo.png"
          width="120"
          height="36"
          alt="Tunis Doyen Rotary Club - Service Above Self"
          style={logoImage}
        />
        <Text style={footer}>
          <Link
            href="https://tunisdoyenrotary.org"
            target="_blank"
            style={{ ...link, color: '#17458F' }}
          >
            Tunis Doyen Rotary Club
          </Link>
          <br />
          Service Above Self • Fellowship • Community Impact
        </Text>
      </Container>
    </Body>
  </Html>
);

NotionMagicLinkEmail.PreviewProps = {
  loginCode: 'sparo-ndigo-amurt-secan',
} as NotionMagicLinkEmailProps;

export default NotionMagicLinkEmail;

const main = {
  backgroundColor: TOKENS.color.neutral.white,
  fontFamily: TOKENS.typography.family.primary,
};

const container = {
  paddingLeft: `${TOKENS.spacing.scale[3]}`,
  paddingRight: `${TOKENS.spacing.scale[3]}`,
  margin: '0 auto',
};

const h1 = {
  color: TOKENS.color.neutral.nearBlack,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.h3,
  fontWeight: TOKENS.typography.weights.bold,
  margin: `${TOKENS.spacing.scale[10]} 0`,
  padding: '0',
};

const link = {
  color: TOKENS.color.brand.royalBlue,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.small,
  textDecoration: 'underline',
};

const text = {
  color: TOKENS.color.neutral.nearBlack,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.small,
  margin: `${TOKENS.spacing.scale[6]} 0`,
};

const footer = {
  color: TOKENS.color.neutral.slate,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.small,
  lineHeight: '22px',
  marginTop: `${TOKENS.spacing.scale[3]}`,
  marginBottom: `${TOKENS.spacing.scale[6]}`,
};

const code = {
  display: 'inline-block',
  padding: `${TOKENS.spacing.scale[4]} ${TOKENS.spacing.scale[1] * 4.5}%`,
  width: '90.5%',
  backgroundColor: TOKENS.color.neutral.smoke,
  borderRadius: TOKENS.radius.sm,
  border: `1px solid ${TOKENS.color.neutral.smoke}`,
  color: TOKENS.color.neutral.nearBlack,
};

const logoImage = {
  maxWidth: '100%',
  height: 'auto',
  display: 'block',
  margin: '0 auto',
};
