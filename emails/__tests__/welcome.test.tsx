import React from 'react';
import { render } from '@testing-library/react';
import { WelcomeEmail } from '../welcome';

describe('WelcomeEmail Component', () => {
  it('should render with default props', () => {
    const { container } = render(<WelcomeEmail />);

    expect(container).toBeInTheDocument();

    // Check for main heading
    const heading = container.querySelector('h1');
    expect(heading).toHaveTextContent('Welcome to Tunis Doyen Rotary Club!');

    // Check for default name
    expect(container.textContent).toContain('Hello Amira');

    // Check for club name
    expect(container.textContent).toContain('Tunis Doyen Rotary Club');

    // Check for CTA button
    const button = container.querySelector('a[href="https://tunisdoyenrotary.org"]');
    expect(button).toBeInTheDocument();
    expect(button).toHaveTextContent('Visit Our Website');
  });

  it('should render with custom props', () => {
    const customProps = {
      name: '<PERSON>',
      clubName: 'Test Club',
      eventDate: '2024-02-15',
      eventName: 'Test Event',
    };

    const { container } = render(<WelcomeEmail {...customProps} />);

    expect(container.textContent).toContain('Hello John Doe');
    expect(container.textContent).toContain('Test Club');
    expect(container.textContent).toContain('Test Event');
    expect(container.textContent).toContain('2024-02-15');
  });

  it('should contain all required sections', () => {
    const { container } = render(<WelcomeEmail />);

    // Check for main sections
    expect(container.textContent).toContain('Welcome to Tunis Doyen Rotary Club!');
    expect(container.textContent).toContain('Service Above Self');
    expect(container.textContent).toContain('Visit Our Website');
    expect(container.textContent).toContain('Get Involved Today');
  });

  it('should have proper email structure', () => {
    const { container } = render(<WelcomeEmail />);

    // Check for HTML structure
    expect(container.querySelector('html')).toBeInTheDocument();
    expect(container.querySelector('head')).toBeInTheDocument();
    expect(container.querySelector('body')).toBeInTheDocument();

    // Check for preview text
    const preview = container.querySelector('title');
    expect(preview).toBeInTheDocument();
  });

  it('should render CTA buttons with correct links', () => {
    const { container } = render(<WelcomeEmail />);

    const websiteButton = container.querySelector('a[href="https://tunisdoyenrotary.org"]');
    expect(websiteButton).toBeInTheDocument();

    const joinButton = container.querySelector('a[href="https://tunisdoyenrotary.org/join"]');
    expect(joinButton).toBeInTheDocument();
    expect(joinButton).toHaveTextContent('Get Involved Today');
  });

  it('should handle empty props gracefully', () => {
    const { container } = render(<WelcomeEmail name="" clubName="" />);

    // Should still render without crashing
    expect(container).toBeInTheDocument();
    expect(container.textContent).toContain('Hello');
  });
});