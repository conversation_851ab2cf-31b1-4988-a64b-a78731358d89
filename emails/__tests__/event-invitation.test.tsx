/**
 * EventInvitationV4 Component - Comprehensive Test Suite
 *
 * Testing strategy:
 * - Unit tests for individual component sections/functions
 * - Integration tests for full component behavior
 * - Accessibility testing for ARIA and semantic compliance
 * - Error handling and fallback scenarios
 */

import React from 'react';
import { render, screen, within } from '@testing-library/react';
import '@testing-library/jest-dom';
import EventInvitationV4 from '../event-invitation';
import { TOKENS } from '../../lib/tokens';

// Mock react-i18next
jest.mock('react-i18next', () => ({
  ...jest.requireActual('react-i18next'),
  initReactI18next: jest.fn(() => ({
    init: jest.fn(),
  })),
  useTranslation: () => ({
    t: (key: string) => key, // Return key as is for testing
  }),
}));

// Mock i18next
jest.mock('i18next', () => ({
  changeLanguage: jest.fn(),
}));

// Mock the i18n module
jest.mock('../../lib/i18n', () => {
  // Do nothing for i18n initialization in tests
});

// Mock next/image if used
jest.mock('next/image', () => ({
  __esModule: true,
  default: ({ src, alt, ...props }: any) => <img src={src} alt={alt} {...props} />
}));

describe('EventInvitationV4 Component', () => {
  // Mock console methods for clean test output
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;

  beforeAll(() => {
    console.error = jest.fn();
    console.warn = jest.fn();
  });

  afterAll(() => {
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
  });

  const defaultProps = {
    eventName: 'Test Rotary Event',
    eventDate: '2024-12-15',
    eventTime: '18:00 - 20:00',
    eventLocation: 'Rotary Club Hall',
    eventDescription: 'An impactful community service event'
  };

  // ============================================================================
  // UNIT TESTS FOR SUB-COMPONENTS AND FUNCTIONS
  // ============================================================================

  describe('EventHeader Sub-component', () => {
    it('displays club branding accurately with logo', () => {
      const clubBranding = {
        name: 'Tunis Doyen Rotary Club',
        logoUrl: 'https://example.com/logo.png'
      };

      render(
        <EventInvitationV4
          {...defaultProps}
          clubBrand={clubBranding}
        />
      );

      const logo = screen.getByAltText(/club logo/i);
      expect(logo).toBeInTheDocument();
      expect(logo).toHaveAttribute('src', clubBranding.logoUrl);
    });

    it('renders placeholder for absent logo with club name as fallback', () => {
      const clubBranding = {
        name: 'Tunis Doyen Rotary Club',
        logoUrl: '' // Empty logo URL
      };

      render(
        <EventInvitationV4
          {...defaultProps}
          clubBrand={clubBranding}
        />
      );

      // Should display club name instead of logo or use alt text
      expect(screen.getByText(clubBranding.name)).toBeInTheDocument();
    });

    it('uses default branding when no club brand provided', () => {
      render(<EventInvitationV4 {...defaultProps} />);

      const defaultLogo = screen.getByAltText(/rotary international/i);
      expect(defaultLogo).toBeInTheDocument();
    });
  });

  describe('EventHero Sub-component', () => {
    it('renders webinar layout with benefits when eventType is webinar', () => {
      const webinarProps = {
        ...defaultProps,
        eventType: 'webinar' as const,
        contentOverrides: {
          whyAttendBenefits: [
            {
              title: 'Expert Networking',
              description: 'Connect with industry leaders',
              icon: '🎤'
            }
          ]
        }
      };

      render(<EventInvitationV4 {...webinarProps} />);

      expect(screen.getByText('Expert Networking')).toBeInTheDocument();
      expect(screen.getByText('Connect with industry leaders')).toBeInTheDocument();
    });

    it('handles different event types (webinar, club, fundraiser, default)', () => {
      const eventTypes: ('webinar' | 'club' | 'fundraiser' | 'default')[] = ['webinar', 'club', 'fundraiser', 'default'];

      eventTypes.forEach(eventType => {
        const { rerender } = render(
          <EventInvitationV4
            {...defaultProps}
            eventType={eventType}
          />
        );

        // Component should render without errors for all event types
        expect(screen.getByText(defaultProps.eventName)).toBeInTheDocument();
      });
    });

    it('applies webinar-specific theming and content structure', () => {
      const webinarProps = {
        ...defaultProps,
        eventType: 'webinar' as const
      };

      render(<EventInvitationV4 {...webinarProps} />);

      // Should contain webinar-specific content based on defaults
      expect(screen.getByText('Discover cutting-edge insights and virtual networking opportunities.')).toBeInTheDocument();
      expect(screen.getByText('Ready to join this webinar?')).toBeInTheDocument();
    });
  });

  describe('EventDetails Sub-component', () => {
    it('displays formatted date/time information', () => {
      render(<EventInvitationV4 {...defaultProps} />);

      expect(screen.getByText('2024-12-15')).toBeInTheDocument();
      expect(screen.getByText('18:00 - 20:00')).toBeInTheDocument();
    });

    it('displays location information correctly', () => {
      render(<EventInvitationV4 {...defaultProps} />);

      expect(screen.getByText('Rotary Club Hall')).toBeInTheDocument();
    });

    it('handles null/undefined event details gracefully', () => {
      render(<EventInvitationV4 />);

      // Should show default values
      expect(screen.getByText('Rotary Event')).toBeInTheDocument();
      expect(screen.getByText('2025-09-04')).toBeInTheDocument();
      expect(screen.getByText('18:00 - 20:00')).toBeInTheDocument();
    });

    it('formats event details in RTL layout for Arabic language', () => {
      render(
        <EventInvitationV4
          {...defaultProps}
          language="ar"
        />
      );

      const htmlElement = document.querySelector('html');
      expect(htmlElement).toHaveAttribute('dir', 'rtl');
    });
  });

  describe('CTAButton Sub-component', () => {
    it('renders with correct styling based on variant', () => {
      const ctaProps = {
        ...defaultProps,
        ctas: [
          {
            text: 'Register Now',
            href: 'https://example.com/register',
            variant: 'primary' as const
          }
        ]
      };

      render(<EventInvitationV4 {...ctaProps} />);

      const button = screen.getByRole('link', { name: /register now/i });
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('href', 'https://example.com/register');
    });

    it('handles accessibility labels correctly', () => {
      const ctaProps = {
        ...defaultProps,
        ctas: [
          {
            text: 'Join Event',
            href: 'https://example.com/join',
            variant: 'secondary' as const,
            ariaLabel: 'Register for the Rotary event'
          }
        ]
      };

      render(<EventInvitationV4 {...ctaProps} />);

      const button = screen.getByRole('link', { name: 'Register for the Rotary event' });
      expect(button).toBeInTheDocument();
    });

    it('applies consistent theming across different variants', () => {
      const ctaProps = {
        ...defaultProps,
        ctas: [
          { text: 'Primary', href: '#', variant: 'primary' as const },
          { text: 'Secondary', href: '#', variant: 'secondary' as const },
          { text: 'Outline', href: '#', variant: 'outline' as const }
        ]
      };

      render(<EventInvitationV4 {...ctaProps} />);

      expect(screen.getByRole('link', { name: /primary/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /secondary/i })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /outline/i })).toBeInTheDocument();
    });
  });

  describe('EventAgenda Sub-component', () => {
    it('displays agenda items with time formatting', () => {
      const agendaProps = {
        ...defaultProps,
        contentOverrides: {
          agenda: [
            {
              time: '18:00 - 18:30',
              description: 'Welcome and Introductions'
            },
            {
              time: '18:30 - 20:00',
              description: 'Main Program'
            }
          ]
        }
      };

      render(<EventInvitationV4 {...agendaProps} />);

      expect(screen.getByText('18:00 - 18:30')).toBeInTheDocument();
      expect(screen.getByText('Welcome and Introductions')).toBeInTheDocument();
      expect(screen.getByText('18:30 - 20:00')).toBeInTheDocument();
      expect(screen.getByText('Main Program')).toBeInTheDocument();
    });

    it('handles empty agenda gracefully', () => {
      render(<EventInvitationV4 {...defaultProps} />);

      // Should not display agenda section when empty
      expect(screen.queryByText('Agenda')).not.toBeInTheDocument();
    });
  });

  describe('EventSpeakers Sub-component', () => {
    it('renders speaker information with photos', () => {
      const speakerProps = {
        ...defaultProps,
        contentOverrides: {
          speakers: [
            {
              name: 'Dr. Sarah Johnson',
              title: 'Community Health Director',
              bio: 'Expert in public health policy',
              imageUrl: 'https://example.com/sarah.jpg'
            }
          ]
        }
      };

      render(<EventInvitationV4 {...speakerProps} />);

      expect(screen.getByText('Dr. Sarah Johnson')).toBeInTheDocument();
      expect(screen.getByText('Community Health Director')).toBeInTheDocument();
      expect(screen.getByText('Expert in public health policy')).toBeInTheDocument();

      const speakerPhoto = screen.getByAltText(/dr\. sarah johnson/i);
      expect(speakerPhoto).toBeInTheDocument();
    });

    it('handles speakers without photos gracefully', () => {
      const speakerProps = {
        ...defaultProps,
        contentOverrides: {
          speakers: [
            {
              name: 'John Smith',
              title: 'Rotary Member',
              bio: 'Active community volunteer'
            }
          ]
        }
      };

      render(<EventInvitationV4 {...speakerProps} />);

      expect(screen.getByText('John Smith')).toBeInTheDocument();
      // Should not display broken images or placeholders
      expect(screen.queryByAltText(/speaker photo/i)).not.toBeInTheDocument();
    });
  });

  describe('EventFooter Sub-component', () => {
    it('displays email footer with correct branding', () => {
      render(<EventInvitationV4 {...defaultProps} />);

      // Should contain footer information
      expect(screen.getByText(/service above self/i)).toBeInTheDocument();
    });

    it('includes contact email and website information', () => {
      const contactProps = {
        ...defaultProps,
        clubBrand: {
          contactEmail: '<EMAIL>',
          websiteUrl: 'https://rotarytunis.site'
        }
      };

      render(<EventInvitationV4 {...contactProps} />);

      expect(document.body.textContent).toContain('<EMAIL>');
      expect(document.body.textContent).toContain('https://rotarytunis.site');
    });
  });

  // ============================================================================
  // INTEGRATION TESTS FOR FULL COMPONENT
  // ============================================================================

  describe('Full Component Integration', () => {
    it('adapts webinar layout faithfully with all benefits and agenda', () => {
      const webinarFullProps = {
        ...defaultProps,
        eventType: 'webinar' as const,
        contentOverrides: {
          agenda: [
            {
              time: '14:00 - 14:15',
              description: 'Opening Remarks'
            }
          ],
          whyAttendBenefits: [
            {
              title: 'Network Virtually',
              description: 'Connect from anywhere'
            }
          ],
          speakers: [
            {
              name: 'Dr. Maria Sanchez',
              title: 'Healthcare Specialist',
              bio: 'Leading healthcare innovation',
              imageUrl: 'https://example.com/maria.jpg'
            }
          ]
        },
        ctas: [
          {
            text: 'Register for Webinar',
            href: 'https://webinar.join',
            variant: 'primary' as const
          }
        ]
      };

      render(<EventInvitationV4 {...webinarFullProps} />);

      // Verify all sections are present and correctly integrated
      expect(screen.getByText(webinarFullProps.eventName)).toBeInTheDocument();
      expect(screen.getByText('14:00 - 14:15')).toBeInTheDocument();
      expect(screen.getByText('Network Virtually')).toBeInTheDocument();
      expect(screen.getByText('Dr. Maria Sanchez')).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /register for webinar/i })).toBeInTheDocument();
    });

    it('manages validation errors via fallback rendering', () => {
      // Suppress console errors for this test
      const { error } = console;

      render(
        <EventInvitationV4
          eventType={'webinar' as any}
          // Invalid props that should trigger validation
        />
      );

      // Should still render successfully despite validation errors
      expect(screen.getByText('Rotary Event')).toBeInTheDocument();

      // Validation warnings should not break rendering
      const { warn } = console;
    });

    it('accommodates RTL language flow for Arabic content', () => {
      const arabicProps = {
        ...defaultProps,
        language: 'ar' as const,
        eventName: 'فعالية الروتاري',
        eventDescription: 'حدث مجتمعي مهم'
      };

      render(<EventInvitationV4 {...arabicProps} />);

      const htmlElement = document.querySelector('html');
      expect(htmlElement).toHaveAttribute('dir', 'rtl');
      expect(htmlElement).toHaveAttribute('lang', 'ar');

      expect(screen.getByText('فعالية الروتاري')).toBeInTheDocument();
    });

    it('handles different event types with appropriate content', () => {
      const eventConfigs = {
        webinar: {
          headline: 'Discover cutting-edge insights',
          benefits: true
        },
        club: {
          headline: 'Rotary Event',
          benefits: false
        },
        fundraiser: {
          headline: 'Support Our Cause',
          benefits: false
        },
        default: {
          headline: 'Rotary Event',
          benefits: false
        }
      };

      Object.entries(eventConfigs).forEach(([eventType, config]) => {
        const { rerender } = render(
          <EventInvitationV4
            {...defaultProps}
            eventType={eventType as any}
          />
        );

        if (config.benefits) {
          // For webinars, check for benefits section
          expect(screen.getByText(config.headline)).toBeInTheDocument();
        } else {
          // For other types, basic rendering
          expect(screen.getByText(defaultProps.eventName)).toBeInTheDocument();
        }
      });
    });

    it('integrates with i18n system correctly', () => {
      // Mock i18next changeLanguage
      const mockChangeLanguage = jest.fn();
      jest.mock('i18next', () => ({
        changeLanguage: mockChangeLanguage
      }));

      render(<EventInvitationV4 {...defaultProps} language="fr" />);

      // Should call changeLanguage for server-side rendering
      expect(mockChangeLanguage).toHaveBeenCalledWith('fr');
    });

    it('applies theming consistently across all sections', () => {
      const themedProps = {
        ...defaultProps,
        theme: {
          primaryColor: '#FF6B35',
          secondaryColor: '#F7931E',
          backgroundColor: '#F8F9FA',
          textColor: '#333333'
        }
      };

      render(<EventInvitationV4 {...themedProps} />);

      // Verify theming is applied (this will be checked by individual sub-component tests)
      const container = screen.getByText(defaultProps.eventName).closest('div');

      expect(container).toBeInTheDocument();
    });
  });

  // ============================================================================
  // ACCESSIBILITY TESTS
  // ============================================================================

  describe('Accessibility Tests', () => {
    it('verifies ARIA labels and roles for interactive elements', () => {
      const accessibleProps = {
        ...defaultProps,
        ctas: [
          {
            text: 'Join Now',
            href: '#',
            variant: 'primary' as const,
            ariaLabel: 'Join the Rotary event now'
          }
        ]
      };

      render(<EventInvitationV4 {...accessibleProps} />);

      const button = screen.getByRole('link', { name: 'Join the Rotary event now' });
      expect(button).toHaveAttribute('aria-label', 'Join the Rotary event now');
    });

    it('checks focus management for interactive elements', () => {
      const interactiveProps = {
        ...defaultProps,
        ctas: [
          {
            text: 'Register',
            href: 'http://example.com',
            variant: 'primary' as const
          }
        ]
      };

      render(<EventInvitationV4 {...interactiveProps} />);

      const button = screen.getByRole('link');
      expect(button).toBeInTheDocument();
      expect(button).toHaveAttribute('href');
    });

    it('validates semantic HTML structure', () => {
      render(<EventInvitationV4 {...defaultProps} />);

      // Check for semantic structure
      expect(document.querySelector('html')).toHaveAttribute('lang');
      expect(document.querySelector('html')).toHaveAttribute('dir', 'ltr');

      // Check headings exist
      const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      expect(headings.length).toBeGreaterThan(0);
    });

    it('ensures sufficient color contrast in themed elements', () => {
      // This test verifies that theme colors are applied correctly
      const themedProps = {
        ...defaultProps,
        theme: {
          primaryColor: '#0066CC', // Good contrast
          secondaryColor: '#FF8800',
          backgroundColor: '#FFFFFF',
          textColor: '#333333'
        }
      };

      render(<EventInvitationV4 {...themedProps} />);

      // Theme should be applied without errors
      expect(screen.getByText(defaultProps.eventName)).toBeInTheDocument();
    });
  });

  // ============================================================================
  // ERROR HANDLING TESTS
  // ============================================================================

  describe('Error Handling Tests', () => {
    it('handles invalid prop combinations with graceful fallbacks', () => {
      // Invalid event type with warnings
      render(
        <EventInvitationV4
          {...defaultProps}
          eventType={'invalid' as any}
        />
      );

      // Should still render with warning
      expect(screen.getByText(defaultProps.eventName)).toBeInTheDocument();
      expect(console.warn).toHaveBeenCalled();
    });

    it('deals with missing required images/assets with placeholders', () => {
      const missingAssetsProps = {
        ...defaultProps,
        clubBrand: {}, // No logo
        media: {
          heroImageUrl: '', // Empty hero image
          eventIconUrl: undefined
        }
      };

      render(<EventInvitationV4 {...missingAssetsProps} />);

      // Should render without broken images
      expect(screen.getByText(defaultProps.eventName)).toBeInTheDocument();

      // Check for any images that might be broken
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        expect(img).toHaveAttribute('src');
        expect(img).toHaveAttribute('alt');
      });
    });

    it('manages validation errors without breaking email rendering', () => {
      const invalidProps = {
        ctas: [
          { text: '', href: 'invalid', variant: 'invalid' as any },
        ] as any // Cast to bypass TypeScript checking for invalid props
      };

      render(<EventInvitationV4 {...defaultProps} {...invalidProps} />);

      // Should render despite validation errors
      expect(screen.getByText(defaultProps.eventName)).toBeInTheDocument();
      expect(console.error).toHaveBeenCalled();
    });

    it('handles null/undefined content overrides gracefully', () => {
      render(
        <EventInvitationV4
          {...defaultProps}
          contentOverrides={undefined}
        />
      );

      expect(screen.getByText(defaultProps.eventName)).toBeInTheDocument();
    });

    it('processes empty arrays for content overrides', () => {
      const emptyOverrides = {
        ...defaultProps,
        contentOverrides: {
          agenda: [],
          speakers: [],
          whyAttendBenefits: []
        },
        ctas: []
      };

      render(<EventInvitationV4 {...emptyOverrides} />);

      // Should render with defaults, no errors
      expect(screen.getByText(defaultProps.eventName)).toBeInTheDocument();
    });
  });

  // ============================================================================
  // EDGE CASES AND SPECIAL SCENARIOS
  // ============================================================================

  describe('Edge Cases', () => {
    it('renders correctly with minimal props', () => {
      render(<EventInvitationV4 />);

      expect(screen.getByText('Rotary Event')).toBeInTheDocument();
      expect(screen.getByText('Join us for an impactful Rotary event.')).toBeInTheDocument();
    });

    it('handles extremely long content without breaking layout', () => {
      const longContent = 'A'.repeat(1000);

      render(
        <EventInvitationV4
          {...defaultProps}
          eventDescription={longContent}
        />
      );

      expect(screen.getByText(longContent)).toBeInTheDocument();
    });

    it('supports custom font families for different languages', () => {
      const arabicProps = {
        ...defaultProps,
        language: 'ar' as const
      };

      render(<EventInvitationV4 {...arabicProps} />);

      // Check for Arabic font family
      expect(document.body.style.fontFamily).toContain('Tajawal');
    });

    it('generates valid Google Calendar URLs', () => {
      // This test checks that the calendar URL generation function works
      render(<EventInvitationV4 {...defaultProps} />);

      // The calendar URL should be generated and included in the component
      const calendarLink = screen.getByRole('link', { name: /add to calendar/i });
      expect(calendarLink).toHaveAttribute('href');
      expect(calendarLink.getAttribute('href')).toContain('calendar.google.com');
    });
  });
});