import { Section, Img } from '@react-email/components';
import { TOKENS } from '../../lib/tokens';

interface EmailHeaderProps {
  logoSrc?: string;
  logoAlt?: string;
  logoWidth?: number;
  logoHeight?: number;
  showLogo?: boolean;
}

export const EmailHeader = ({
  logoSrc = 'https://res.cloudinary.com/deerc1s7z/image/upload/v1756761035/Rotary_Logo_FR21_g8kme3.png',
  logoAlt = 'Tunis Doyen Rotary Club - Place à l’action', // Place à l’action
  logoWidth = 120,
  logoHeight = 36,
  showLogo = true,
}: EmailHeaderProps) => {
  return (
    <Section align="center" style={headerContainer}>
      {showLogo ? (
        <Img
          src={logoSrc}
          width={logoWidth}
          height={logoHeight}
          alt={logoAlt}
          style={logoImage}
        />
      ) : (
        <p style={titleStyle} role="heading" aria-level={1}>
          We are Rotary. We are People of Action.
        </p>
      )}
    </Section>
  );
};

const headerContainer = {
  textAlign: 'center' as const,
  padding: `${TOKENS.spacing.scale[5]} 0`,
};

const logoImage = {
  maxWidth: '100%',
  height: 'auto',
  display: 'block',
  margin: '0 auto',
};

const titleStyle = {
  fontSize: TOKENS.typography.scale.body,
  fontWeight: TOKENS.typography.weights.bold,
  color: TOKENS.color.brand.royalBlue,
  margin: 0,
  textAlign: 'center' as const,
};
