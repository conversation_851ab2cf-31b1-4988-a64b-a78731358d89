import React from 'react';
import { Section, Heading, Img } from '@react-email/components';
import { TOKENS } from '../../lib/tokens';

interface EmailHeaderProps {
  logoUrl?: string;
  title: string;
  subtitle?: string;
  backgroundColor?: string;
}

export const EmailHeader: React.FC<EmailHeaderProps> = ({
  logoUrl = 'https://res.cloudinary.com/deerc1s7z/image/upload/v1756761035/RotaryMoE-R_CMYK-C_xko6yo.png',
  title,
  subtitle,
  backgroundColor = TOKENS.color.brand.royalBlue
}) => {
  return (
    <Section style={{
      backgroundColor,
      padding: '32px 24px',
      textAlign: 'center' as const,
    }}>
      {logoUrl && (
        <Img
          src={logoUrl}
          width={120}
          height={60}
          alt="Rotary Logo"
          style={{
            display: 'block',
            margin: '0 auto 16px auto',
          }}
        />
      )}
      <Heading style={{
        fontSize: '24px',
        color: TOKENS.color.neutral.white,
        margin: '0 0 8px 0',
        fontWeight: TOKENS.typography.weights.bold,
        fontFamily: TOKENS.typography.family.primary,
      }}>
        {title}
      </Heading>
      {subtitle && (
        <Heading style={{
          fontSize: '16px',
          color: TOKENS.color.neutral.white,
          margin: 0,
          fontWeight: TOKENS.typography.weights.regular,
          fontFamily: TOKENS.typography.family.primary,
          opacity: 0.9,
        }}>
          {subtitle}
        </Heading>
      )}
    </Section>
  );
};