import React from 'react';
import { render } from '@testing-library/react';
import { CTAButton } from '../CTAButton';
import { TOKENS } from '../../../lib/tokens';

describe('CTAButton Component', () => {
  it('should render with default props', () => {
    const { container } = render(
      <CTAButton href="https://example.com">Click me</CTAButton>
    );

    const link = container.querySelector('a');
    expect(link).toBeInTheDocument();
    expect(link).toHaveAttribute('href', 'https://example.com');
    expect(link).toHaveTextContent('Click me');

    // Check default primary variant styles
    expect(link).toHaveStyle({
      backgroundColor: TOKENS.color.brand.royalBlue,
      color: TOKENS.color.neutral.white,
    });
  });

  it('should render with secondary variant', () => {
    const { container } = render(
      <CTAButton href="https://example.com" variant="secondary">
        Secondary Button
      </CTAButton>
    );

    const link = container.querySelector('a');
    expect(link).toHaveStyle({
      backgroundColor: TOKENS.color.neutral.white,
      color: TOKENS.color.brand.royalBlue,
      border: `2px solid ${TOKENS.color.brand.royalBlue}`,
    });
  });

  it('should render with gold variant', () => {
    const { container } = render(
      <CTAButton href="https://example.com" variant="gold">
        Gold Button
      </CTAButton>
    );

    const link = container.querySelector('a');
    expect(link).toHaveStyle({
      backgroundColor: TOKENS.color.brand.gold,
      color: TOKENS.color.neutral.nearBlack,
    });
  });

  it('should render with different sizes', () => {
    const { container: smallContainer } = render(
      <CTAButton href="https://example.com" size="sm">
        Small
      </CTAButton>
    );

    const { container: mediumContainer } = render(
      <CTAButton href="https://example.com" size="md">
        Medium
      </CTAButton>
    );

    const { container: largeContainer } = render(
      <CTAButton href="https://example.com" size="lg">
        Large
      </CTAButton>
    );

    const smallLink = smallContainer.querySelector('a');
    const mediumLink = mediumContainer.querySelector('a');
    const largeLink = largeContainer.querySelector('a');

    expect(smallLink).toHaveStyle({
      fontSize: TOKENS.typography.scale.small,
      padding: `${TOKENS.spacing.scale[2]} ${TOKENS.spacing.scale[4]}`,
    });

    expect(mediumLink).toHaveStyle({
      fontSize: TOKENS.typography.scale.body,
      padding: `${TOKENS.spacing.scale[3]} ${TOKENS.spacing.scale[6]}`,
    });

    expect(largeLink).toHaveStyle({
      fontSize: TOKENS.typography.scale.h3,
      padding: `${TOKENS.spacing.scale[4]} ${TOKENS.spacing.scale[8]}`,
    });
  });

  it('should render as full width when specified', () => {
    const { container } = render(
      <CTAButton href="https://example.com" fullWidth>
        Full Width
      </CTAButton>
    );

    const link = container.querySelector('a');
    expect(link).toHaveStyle({
      width: '100%',
      display: 'block',
    });
  });

  it('should apply custom styles', () => {
    const customStyle = { marginTop: '20px' };

    const { container } = render(
      <CTAButton href="https://example.com" style={customStyle}>
        Custom Style
      </CTAButton>
    );

    const link = container.querySelector('a');
    expect(link).toHaveStyle(customStyle);
  });

  it('should have proper base styles', () => {
    const { container } = render(
      <CTAButton href="https://example.com">Base Styles</CTAButton>
    );

    const link = container.querySelector('a');
    expect(link).toHaveStyle({
      fontFamily: TOKENS.typography.family.primary,
      fontWeight: TOKENS.typography.weights.bold,
      textDecoration: 'none',
      textAlign: 'center',
      display: 'inline-block',
      borderRadius: TOKENS.radius.sm,
      boxShadow: TOKENS.shadow.sm,
      transition: 'all 0.2s ease',
    });
  });

  it('should render children correctly', () => {
    const { container } = render(
      <CTAButton href="https://example.com">
        <span>Button Text</span>
      </CTAButton>
    );

    const link = container.querySelector('a');
    expect(link).toHaveTextContent('Button Text');
  });
});