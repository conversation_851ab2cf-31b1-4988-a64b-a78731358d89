import { TOKENS } from '../../lib/tokens';

// Shared styling constants for email templates
export const emailStyles = {
  // Layout
  main: {
    backgroundColor: TOKENS.color.neutral.white,
    fontFamily: TOKENS.typography.family.primary,
  },

  container: {
    margin: '0 auto',
    padding: `${TOKENS.spacing.scale[5]} 0 ${TOKENS.spacing.scale[12]}`,
    width: '560px',
  },

  // Typography
  h1: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.h3,
    fontWeight: TOKENS.typography.weights.bold,
    margin: `${TOKENS.spacing.scale[10]} 0`,
    padding: '0',
    textAlign: 'center' as const,
  },

  h2: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.h2,
    fontWeight: TOKENS.typography.weights.bold,
    margin: `${TOKENS.spacing.scale[7]} 0`,
    padding: '0',
    textAlign: 'center' as const,
  },

  h3: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.h3,
    fontWeight: TOKENS.typography.weights.bold,
    margin: `${TOKENS.spacing.scale[5]} 0`,
    padding: '0',
  },

  text: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    lineHeight: '26px',
  },

  smallText: {
    color: TOKENS.color.neutral.charcoal,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.small,
    lineHeight: '20px',
  },

  // Components
  section: {
    margin: `${TOKENS.spacing.scale[7]} 0`,
  },

  card: {
    backgroundColor: TOKENS.color.neutral.smoke,
    borderRadius: TOKENS.radius.sm,
    padding: `${TOKENS.spacing.scale[5]}`,
    margin: `${TOKENS.spacing.scale[5]} 0`,
  },

  // Utility
  center: {
    textAlign: 'center' as const,
  },

  left: {
    textAlign: 'left' as const,
  },

  logoImage: {
    maxWidth: '100%',
    height: 'auto',
    display: 'block',
    margin: '0 auto',
  },
};

// Rotary-specific constants
export const ROTARY_CONSTANTS = {
  clubName: 'Tunis Doyen Rotary Club',
  motto: 'Service Above Self',
  email: '<EMAIL>',
  phone: '+216 XX XXX XXX',
  website: 'https://rotarytunisdoyen.site',
  facebook: 'https://facebook.com/tunisdoyenrotary',
  linkedin: 'https://linkedin.com/company/tunis-doyen-rotary',
  logoSrc: '/static/Rotary_logo.png',
  logoAlt: 'Tunis Doyen Rotary Club - Service Above Self',
};