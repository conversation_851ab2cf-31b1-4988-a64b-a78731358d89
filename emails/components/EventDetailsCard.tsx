import React from 'react';
import { Section, Text, Heading } from '@react-email/components';
import { TOKENS } from '../../lib/tokens';

interface EventDetailsCardProps {
  eventName: string;
  eventDate: string;
  eventTime: string;
  eventLocation?: string;
  eventDescription?: string;
}

export const EventDetailsCard: React.FC<EventDetailsCardProps> = ({
  eventName,
  eventDate,
  eventTime,
  eventLocation,
  eventDescription
}) => {
  const cardStyle = {
    backgroundColor: TOKENS.color.neutral.white,
    border: `2px solid ${TOKENS.color.brand.gold}`,
    borderRadius: TOKENS.radius.md,
    padding: '24px',
    margin: '20px 0',
  };

  const detailRowStyle = {
    display: 'flex',
    alignItems: 'center',
    margin: '12px 0',
    gap: '12px',
  };

  const iconStyle = {
    width: '20px',
    height: '20px',
    backgroundColor: TOKENS.color.brand.royalBlue,
    borderRadius: '50%',
    display: 'inline-block',
    flexShrink: 0,
  };

  const labelStyle = {
    fontWeight: TOKENS.typography.weights.bold,
    color: TOKENS.color.brand.royalBlue,
    fontSize: '14px',
    fontFamily: TOKENS.typography.family.primary,
    margin: 0,
  };

  const valueStyle = {
    color: TOKENS.color.neutral.nearBlack,
    fontSize: '14px',
    fontFamily: TOKENS.typography.family.primary,
    margin: 0,
  };

  return (
    <Section style={cardStyle}>
      <Heading style={{
        fontSize: '20px',
        fontWeight: TOKENS.typography.weights.bold,
        color: TOKENS.color.brand.royalBlue,
        margin: '0 0 16px 0',
        fontFamily: TOKENS.typography.family.primary,
      }}>
        {eventName}
      </Heading>

      <div style={detailRowStyle}>
        <div style={iconStyle}></div>
        <div>
          <Text style={labelStyle}>DATE:</Text>
          <Text style={valueStyle}>{eventDate}</Text>
        </div>
      </div>

      <div style={detailRowStyle}>
        <div style={iconStyle}></div>
        <div>
          <Text style={labelStyle}>TIME:</Text>
          <Text style={valueStyle}>{eventTime}</Text>
        </div>
      </div>

      {eventLocation && (
        <div style={detailRowStyle}>
          <div style={iconStyle}></div>
          <div>
            <Text style={labelStyle}>LOCATION:</Text>
            <Text style={valueStyle}>{eventLocation}</Text>
          </div>
        </div>
      )}

      {eventDescription && (
        <Text style={{
          fontSize: '16px',
          color: TOKENS.color.neutral.nearBlack,
          lineHeight: 1.6,
          margin: '16px 0 0 0',
          fontFamily: TOKENS.typography.family.primary,
        }}>
          {eventDescription}
        </Text>
      )}
    </Section>
  );
};