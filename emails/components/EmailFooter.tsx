import { Section, Text, Link, Img } from '@react-email/components';
import { TOKENS } from '../../lib/tokens';
import { ROTARY_CONSTANTS } from './styles';
import { useTranslation } from 'react-i18next';
import '../../lib/i18n';
import i18next from 'i18next';

interface EmailFooterProps {
  clubName?: string;
  email?: string;
  phone?: string;
  websiteUrl?: string;
  facebookUrl?: string;
  linkedinUrl?: string;
  showDisclaimer?: boolean;
  language?: 'en' | 'fr' | 'ar';
}

const defaultProps = {
  clubName: ROTARY_CONSTANTS.clubName,
  email: ROTARY_CONSTANTS.email,
  phone: ROTARY_CONSTANTS.phone,
  websiteUrl: ROTARY_CONSTANTS.website,
  facebookUrl: ROTARY_CONSTANTS.facebook,
  linkedinUrl: ROTARY_CONSTANTS.linkedin,
  showDisclaimer: true,
};

const EmailFooterInner = (props: Omit<EmailFooterProps, 'language'>) => {
  const { t } = useTranslation();

  const {
    clubName = defaultProps.clubName,
    email = defaultProps.email,
    phone = defaultProps.phone,
    websiteUrl = defaultProps.websiteUrl,
    facebookUrl = defaultProps.facebookUrl,
    linkedinUrl = defaultProps.linkedinUrl,
    showDisclaimer = defaultProps.showDisclaimer,
  } = props;

  return (
    <Section style={footer}>
      {/* Rotary Logo */}
      <Img
        src="https://res.cloudinary.com/deerc1s7z/image/upload/v1756761035/POA_StackedLockup_CMYK_FR_tl9koa.png"
        alt={`${clubName} logo`}
        width={120}
        height={120}
        style={footerLogo}
      />

      <Text style={footerText}>
        <strong>{clubName}</strong>
      </Text>
      <Text style={footerText}>
        {t('footer.motto')}
      </Text>
      <Text style={footerText}>
        {t('footer.location')} | {email} | {phone}
      </Text>

      <Section style={footerLinks}>
        <Link href={websiteUrl} style={footerLink} aria-label={`${clubName} ${t('footer.websiteLabel')}`}>
          {t('footer.website')}
        </Link> |
        <Link href={facebookUrl} style={footerLink} aria-label={`${clubName} Facebook`}>
          {t('footer.facebook')}
        </Link> |
        <Link href={linkedinUrl} style={footerLink} aria-label={`${clubName} LinkedIn`}>
          LinkedIn
        </Link>
      </Section>

      {showDisclaimer && (
        <Text style={footerDisclaimer}>
          {t('footer.disclaimer', { clubName })}
        </Text>
      )}
    </Section>
  );
};

export const EmailFooter = ({ language = 'en', ...props }: EmailFooterProps = {}) => {
  // Set language for footer component
  if (typeof window === 'undefined') {
    i18next.changeLanguage(language);
  }

  return <EmailFooterInner {...props} />;
};

const footer = {
  marginTop: `${TOKENS.spacing.scale[10]}`,
  textAlign: 'center' as const,
  borderTop: `2px solid ${TOKENS.color.brand.royalBlue}`,
  padding: `${TOKENS.spacing.scale[5]}`,
  backgroundColor: TOKENS.color.neutral.smoke,
  borderRadius: TOKENS.radius.sm,
};

const footerLogo = {
  display: 'block',
  margin: '0 auto',
  maxWidth: '120px',
  height: 'auto',
};

const footerText = {
  color: TOKENS.color.neutral.charcoal,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.small,
  margin: `${TOKENS.spacing.scale[1]} 0`,
  padding: '0',
  lineHeight: '1.4',
};

const footerLinks = {
  margin: `${TOKENS.spacing.scale[3]} 0`,
};

const footerLink = {
  color: TOKENS.color.brand.royalBlue,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.small,
  textDecoration: 'underline',
  margin: `0 ${TOKENS.spacing.scale[2]}`,
};

const footerDisclaimer = {
  color: TOKENS.color.neutral.slate,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.small,
  fontStyle: 'italic',
  margin: `${TOKENS.spacing.scale[3]} 0 0 0`,
  padding: '0',
  lineHeight: '1.3',
};
