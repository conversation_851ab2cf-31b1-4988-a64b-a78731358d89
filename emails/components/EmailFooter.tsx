import React from 'react';
import { Section, Text, Link } from '@react-email/components';
import { TOKENS } from '../../lib/tokens';

interface EmailFooterProps {
  clubName?: string;
  contactEmail?: string;
  websiteUrl?: string;
  backgroundColor?: string;
}

export const EmailFooter: React.FC<EmailFooterProps> = ({
  clubName = 'Rotary International',
  contactEmail,
  websiteUrl,
  backgroundColor = TOKENS.color.brand.royalBlue
}) => {
  return (
    <Section style={{
      backgroundColor,
      padding: '32px 24px',
      textAlign: 'center' as const,
    }}>
      <Text style={{
        fontSize: '16px',
        color: TOKENS.color.neutral.white,
        fontWeight: TOKENS.typography.weights.bold,
        margin: '0 0 12px 0',
        fontFamily: TOKENS.typography.family.primary,
      }}>
        We are People of Action
      </Text>
      
      <Text style={{
        fontSize: '14px',
        color: TOKENS.color.neutral.white,
        margin: '0 0 16px 0',
        fontFamily: TOKENS.typography.family.primary,
        opacity: 0.9,
      }}>
        Together, we take action to create lasting change in our communities and around the world.
      </Text>
      
      {(contactEmail || websiteUrl) && (
        <div style={{ margin: '16px 0' }}>
          {contactEmail && (
            <Text style={{
              fontSize: '12px',
              color: TOKENS.color.neutral.white,
              margin: '4px 0',
              fontFamily: TOKENS.typography.family.primary,
              opacity: 0.8,
            }}>
              {clubName} | {contactEmail}
            </Text>
          )}
          
          {websiteUrl && (
            <Link href={websiteUrl} style={{
              fontSize: '12px',
              color: TOKENS.color.neutral.white,
              textDecoration: 'underline',
              fontFamily: TOKENS.typography.family.primary,
              opacity: 0.8,
            }}>
              Visit our website
            </Link>
          )}
        </div>
      )}

      <Text style={{
        fontSize: '11px',
        color: TOKENS.color.neutral.white,
        margin: '16px 0 0 0',
        fontFamily: TOKENS.typography.family.primary,
        opacity: 0.7,
      }}>
        © 2025 Rotary International. All rights reserved.
      </Text>
    </Section>
  );
};