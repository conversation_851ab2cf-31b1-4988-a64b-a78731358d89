import { Link } from '@react-email/components';
import { TOKENS } from '../../lib/tokens';

interface CTAButtonProps {
  href: string;
  children: React.ReactNode;
  variant?: 'primary' | 'secondary' | 'gold';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  style?: React.CSSProperties;
}

export const CTAButton = ({
  href,
  children,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  style = {},
}: CTAButtonProps) => {
  const buttonStyle = {
    ...baseButtonStyle,
    ...variantStyles[variant],
    ...sizeStyles[size],
    ...(fullWidth && { width: '100%', display: 'block' }),
    ...style,
  };

  return (
    <Link href={href} style={buttonStyle}>
      {children}
    </Link>
  );
};

const baseButtonStyle = {
  fontFamily: TOKENS.typography.family.primary,
  fontWeight: TOKENS.typography.weights.bold,
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'inline-block',
  borderRadius: TOKENS.radius.sm,
  boxShadow: TOKENS.shadow.sm,
  transition: 'all 0.2s ease',
};

const variantStyles = {
  primary: {
    backgroundColor: TOKENS.color.brand.royalBlue,
    color: TOKENS.color.neutral.white,
  },
  secondary: {
    backgroundColor: TOKENS.color.neutral.white,
    color: TOKENS.color.brand.royalBlue,
    border: `2px solid ${TOKENS.color.brand.royalBlue}`,
  },
  gold: {
    backgroundColor: TOKENS.color.brand.gold,
    color: TOKENS.color.neutral.nearBlack,
  },
};

const sizeStyles = {
  sm: {
    fontSize: TOKENS.typography.scale.small,
    padding: `${TOKENS.spacing.scale[2]} ${TOKENS.spacing.scale[4]}`,
  },
  md: {
    fontSize: TOKENS.typography.scale.body,
    padding: `${TOKENS.spacing.scale[3]} ${TOKENS.spacing.scale[6]}`,
  },
  lg: {
    fontSize: TOKENS.typography.scale.h3,
    padding: `${TOKENS.spacing.scale[4]} ${TOKENS.spacing.scale[8]}`,
  },
};