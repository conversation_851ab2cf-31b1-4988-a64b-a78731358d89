import React from 'react';
import { Section, Button as ReactEmailButton } from '@react-email/components';
import { TOKENS } from '../../lib/tokens';

interface CTAButton {
  text: string;
  href: string;
  variant: 'primary' | 'secondary';
  ariaLabel?: string;
}

interface CTAButtonGroupProps {
  buttons: CTAButton[];
}

export const CTAButtonGroup: React.FC<CTAButtonGroupProps> = ({ buttons }) => {
  const getButtonStyle = (variant: 'primary' | 'secondary') => {
    const baseStyle = {
      borderRadius: TOKENS.radius.sm,
      display: 'inline-block',
      fontSize: '16px',
      fontWeight: TOKENS.typography.weights.bold,
      fontFamily: TOKENS.typography.family.primary,
      textAlign: 'center' as const,
      textDecoration: 'none',
      margin: '8px 12px',
      minWidth: '160px',
      padding: '14px 28px',
      border: 'none',
      cursor: 'pointer',
    };

    if (variant === 'primary') {
      return {
        ...baseStyle,
        backgroundColor: TOKENS.color.brand.royalBlue,
        color: TOKENS.color.neutral.white,
      };
    } else {
      return {
        ...baseStyle,
        backgroundColor: 'transparent',
        color: TOKENS.color.brand.royalBlue,
        border: `2px solid ${TOKENS.color.brand.royalBlue}`,
      };
    }
  };

  return (
    <Section style={{
      textAlign: 'center' as const,
      padding: '24px 0',
    }}>
      {buttons.map((button, index) => (
        <ReactEmailButton
          key={index}
          href={button.href}
          style={getButtonStyle(button.variant)}
          aria-label={button.ariaLabel || button.text}
        >
          {button.text}
        </ReactEmailButton>
      ))}
    </Section>
  );
};