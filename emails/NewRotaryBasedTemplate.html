<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="x-apple-disable-message-reformatting">
    <meta name="color-scheme" content="light">
    <meta name="supported-color-schemes" content="light">
    <title>{{emailSubject}}</title>
    <!-- Open Sans Font for email compatibility -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <!--[if mso]>
    <style type="text/css">
        .outlook-wrapper { width: 600px; margin: 0 auto; }
        .outlook-text { font-family: Arial, sans-serif !important; }
    </style>
    <![endif]-->
    <style type="text/css">
        /* Reset and base styles */
        body { margin: 0; padding: 0; font-family: 'Open Sans', Arial, sans-serif; background-color: #ffffff; }
        table { border-collapse: collapse; border-spacing: 0; }
        img { outline: none; border: 0; display: block; }
        a { color: inherit; text-decoration: none; }
        .email-wrapper { max-width: 600px; margin: 0 auto; }

        /* Button styles */
        .btn-primary {
            display: inline-block;
            padding: 12px 32px;
            background-color: #17458F;
            color: #ffffff !important;
            border-radius: 4px;
            text-align: center;
            font-family: 'Open Sans', Arial, sans-serif;
            font-size: 16px;
            font-weight: 500;
            line-height: 1.5;
            text-decoration: none;
            border: none;
            outline: none;
        }
        .btn-primary:hover, .btn-primary:focus {
            background-color: #0f3472;
        }

        .btn-secondary {
            display: inline-block;
            padding: 12px 32px;
            background-color: #ffffff;
            color: #F7A81B !important;
            border: 2px solid #F7A81B !important;
            border-radius: 4px;
            text-align: center;
            font-family: 'Open Sans', Arial, sans-serif;
            font-size: 16px;
            font-weight: 500;
            line-height: 1.5;
            text-decoration: none;
            outline: none;
        }
        .btn-secondary:hover, .btn-secondary:focus {
            background-color: #F7A81B;
            color: #ffffff !important;
        }
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #ffffff;">
    <div class="email-wrapper" style="max-width: 600px; margin: 0 auto; background-color: #ffffff; position: relative;">

        <!-- Header -->
        <div style="background-color: #17458F; height: 66px; width: 100%; display: flex; align-items: center; justify-content: center; position: relative;">
            <div style="font-family: 'Open Sans', Arial, sans-serif; font-size: 28px; font-weight: 600; line-height: 34px; color: #ffffff;">{{logoText}}</div>
        </div>

        <!-- Header Gradient -->
        <div style="background-image: linear-gradient(to bottom, #17458F, #0f3472); height: 25px; width: 100%;"></div>

        <!-- Header Subtitle -->
        <div style="background-color: #fcfcfc; padding: 16px 40px;">
            <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 14px; font-weight: 400; line-height: 20px; color: #17458F; margin: 0; text-align: center;">{{headerSubtitle}}</p>
        </div>

        <!-- Hero Image -->
        <div style="display: flex; justify-content: center; padding: 16px 48px;">
            <div style="background-image: url('{{heroImage}}'); background-size: cover; background-position: center; background-repeat: no-repeat; height: 322px; width: 504px; border-radius: 8px; background-color: #e0e0e0;">
                <!-- Fallback placeholder if image doesn't load -->
            </div>
        </div>

        <!-- Event Title Section -->
        <div style="background-color: rgba(23, 69, 143, 0.15); padding: 24px 48px;">
            <h1 style="font-family: 'Open Sans', Arial, sans-serif; font-size: 28px; font-weight: 600; line-height: 34px; color: #17458F; margin: 0; text-align: center;">
                {{eventTitle}}
            </h1>
        </div>

        <!-- Event Description -->
        <div style="padding: 24px 48px;">
            <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 16px; font-weight: 400; line-height: 24px; color: #282828; margin: 0; max-width: 422px; margin: 0 auto; text-align: center;">
                {{eventDescription}}
            </p>
        </div>

        <!-- CTA Button -->
        <div style="display: flex; justify-content: center; padding-bottom: 24px;">
            <a href="{{ctaLink}}" class="btn-primary" style="display: inline-block; padding: 12px 32px; background-color: #17458F; color: #ffffff !important; border-radius: 4px; text-align: center; font-family: 'Open Sans', Arial, sans-serif; font-size: 16px; font-weight: 500; line-height: 1.5; text-decoration: none;">
                Secure My Spot
            </a>
        </div>

        <!-- Event Details -->
        <div style="background-color: #fcfcfc; padding: 24px;">
            <div style="display: flex; align-items: center; justify-content: center;">
                <div style="display: flex; align-items: center; gap: 32px; text-align: center;">
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 46px; height: 43px; display: flex; align-items: center; justify-content: center;">
                            <!-- Calendar icon placeholder -->
                            <img src="{{calendarIcon}}" alt="Calendar" style="width: 100%; height: 100%; object-fit: contain;">
                        </div>
                        <div>
                            <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 14px; font-weight: 500; line-height: 20px; color: #282828; margin: 0;">DATE:</p>
                            <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 14px; font-weight: 400; line-height: 20px; color: #282828; margin: 0;">{{eventDate}}</p>
                        </div>
                    </div>

                    <div style="width: 1px; height: 34px; background-color: #d1d5db;"></div>

                    <div style="display: flex; align-items: center; gap: 8px;">
                        <div style="width: 43px; height: 43px; display: flex; align-items: center; justify-content: center;">
                            <!-- Clock icon placeholder -->
                            <img src="{{clockIcon}}" alt="Clock" style="width: 100%; height: 100%; object-fit: contain;">
                        </div>
                        <div>
                            <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 14px; font-weight: 500; line-height: 20px; color: #282828; margin: 0;">TIME:</p>
                            <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 14px; font-weight: 400; line-height: 20px; color: #282828; margin: 0;">{{eventTime}}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Inside the Event Section -->
        <div style="padding: 48px 48px;">
            <h2 style="font-family: 'Open Sans', Arial, sans-serif; font-size: 28px; font-weight: 600; line-height: 34px; color: #17458F; margin: 0 0 16px 0;">{{sectionTitle}}</h2>
            <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 16px; font-weight: 400; line-height: 24px; color: #282828; margin: 0 0 24px 0;">
                {{sectionDescription}}
            </p>

            <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 16px; font-weight: 400; line-height: 24px; color: #F7A81B; margin: 0 0 24px 0;">What You'll Discover:</p>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 24px;">
                <div style="text-align: center;">
                    <div style="width: 35px; height: 35px; margin: 0 auto 12px;">
                        <img src="{{featureIcon1}}" alt="Innovation" style="width: 100%; height: 100%; object-fit: contain;">
                    </div>
                    <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 14px; font-weight: 400; line-height: 20px; color: #282828; margin: 0;">
                        {{feature1Text}}
                    </p>
                </div>

                <div style="text-align: center;">
                    <div style="width: 35px; height: 35px; margin: 0 auto 12px;">
                        <img src="{{featureIcon2}}" alt="Tools" style="width: 100%; height: 100%; object-fit: contain;">
                    </div>
                    <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 14px; font-weight: 400; line-height: 20px; color: #282828; margin: 0;">
                        {{feature2Text}}
                    </p>
                </div>

                <div style="text-align: center;">
                    <div style="width: 35px; height: 35px; margin: 0 auto 12px;">
                        <img src="{{featureIcon3}}" alt="Networking" style="width: 100%; height: 100%; object-fit: contain;">
                    </div>
                    <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 14px; font-weight: 400; line-height: 20px; color: #282828; margin: 0;">
                        {{feature3Text}}
                    </p>
                </div>
            </div>
        </div>

        <!-- Speakers Section -->
        <div style="padding: 48px 48px;">
            <h2 style="font-family: 'Open Sans', Arial, sans-serif; font-size: 28px; font-weight: 600; line-height: 34px; color: #17458F; margin: 0 0 32px 0;">Speakers</h2>

            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 32px;">
                <div style="text-align: center;">
                    <div style="width: 77px; height: 77px; margin: 0 auto 16px;">
                        <img src="{{speaker1Image}}" alt="{{speaker1Name}}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                    </div>
                    <div style="width: 100%; height: 1px; background-color: #d1d5db; margin: 0 0 12px 0;"></div>
                    <h3 style="font-family: 'Open Sans', Arial, sans-serif; font-size: 16px; font-weight: 400; line-height: 24px; color: #000000; margin: 0 0 4px 0;">{{speaker1Name}}</h3>
                    <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 12px; font-weight: 400; line-height: 16px; color: #F7A81B; margin: 0 0 12px 0;">{{speaker1Title}}</p>
                    <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 12px; font-weight: 400; line-height: 16px; color: #282828; margin: 0;">
                        {{speaker1Bio}}
                    </p>
                </div>

                <div style="text-align: center;">
                    <div style="width: 77px; height: 77px; margin: 0 auto 16px;">
                        <img src="{{speaker2Image}}" alt="{{speaker2Name}}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                    </div>
                    <div style="width: 100%; height: 1px; background-color: #d1d5db; margin: 0 0 12px 0;"></div>
                    <h3 style="font-family: 'Open Sans', Arial, sans-serif; font-size: 16px; font-weight: 400; line-height: 24px; color: #000000; margin: 0 0 4px 0;">{{speaker2Name}}</h3>
                    <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 12px; font-weight: 400; line-height: 16px; color: #F7A81B; margin: 0 0 12px 0;">{{speaker2Title}}</p>
                    <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 12px; font-weight: 400; line-height: 16px; color: #282828; margin: 0;">
                        {{speaker2Bio}}
                    </p>
                </div>

                <div style="text-align: center;">
                    <div style="width: 77px; height: 77px; margin: 0 auto 16px;">
                        <img src="{{speaker3Image}}" alt="{{speaker3Name}}" style="width: 100%; height: 100%; border-radius: 50%; object-fit: cover;">
                    </div>
                    <div style="width: 100%; height: 1px; background-color: #d1d5db; margin: 0 0 12px 0;"></div>
                    <h3 style="font-family: 'Open Sans', Arial, sans-serif; font-size: 16px; font-weight: 400; line-height: 24px; color: #000000; margin: 0 0 4px 0;">{{speaker3Name}}</h3>
                    <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 12px; font-weight: 400; line-height: 16px; color: #F7A81B; margin: 0 0 12px 0;">{{speaker3Title}}</p>
                    <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 12px; font-weight: 400; line-height: 16px; color: #282828; margin: 0;">
                        {{speaker3Bio}}
                    </p>
                </div>
            </div>
        </div>

        <!-- RSVP Section -->
        <div style="background-color: #fcfcfc; padding: 32px;">
            <div style="text-align: center;">
                <h2 style="font-family: 'Open Sans', Arial, sans-serif; font-size: 22px; font-weight: 600; line-height: 28px; color: #17458F; margin: 0 0 24px 0;">{{rsvpQuestion}}</h2>
                <div style="display: flex; justify-content: center; gap: 16px;">
                    <a href="{{rsvpYesLink}}" class="btn-primary" style="display: inline-block; padding: 12px 24px; background-color: #17458F; color: #ffffff !important; border-radius: 4px; text-align: center; font-family: 'Open Sans', Arial, sans-serif; font-size: 16px; font-weight: 500; line-height: 1.5; text-decoration: none;">
                        Yes
                    </a>
                    <a href="{{rsvpNoLink}}" class="btn-secondary" style="display: inline-block; padding: 12px 24px; background-color: #ffffff; color: #F7A81B !important; border: 2px solid #F7A81B; border-radius: 4px; text-align: center; font-family: 'Open Sans', Arial, sans-serif; font-size: 16px; font-weight: 500; line-height: 1.5; text-decoration: none;">
                        No
                    </a>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div style="background-color: #0f3472; padding: 32px 48px;">
            <div style="display: flex; justify-content: center; gap: 24px; margin-bottom: 16px;">
                <div style="width: 23px; height: 23px; display: flex; align-items: center; justify-content: center; background-color: #ffffff; border-radius: 50%;">
                    <img src="{{facebookIcon}}" alt="Facebook" style="width: 60%; height: 60%; object-fit: contain;">
                </div>
                <div style="width: 23px; height: 23px; display: flex; align-items: center; justify-content: center; background-color: #ffffff; border-radius: 50%;">
                    <img src="{{twitterIcon}}" alt="Twitter" style="width: 60%; height: 60%; object-fit: contain;">
                </div>
                <div style="width: 23px; height: 23px; display: flex; align-items: center; justify-content: center; background-color: #ffffff; border-radius: 50%;">
                    <img src="{{linkedinIcon}}" alt="LinkedIn" style="width: 60%; height: 60%; object-fit: contain;">
                </div>
            </div>

            <div style="text-align: center; color: #ffffff;">
                <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 14px; font-weight: 400; line-height: 20px; margin: 0 0 4px 0;">
                    Home | Support | Our Policy | Member Area
                </p>
                <p style="font-family: 'Open Sans', Arial, sans-serif; font-size: 14px; font-weight: 400; line-height: 20px; margin: 0 0 16px 0;">
                    You're receiving this email because you signed up for our {{emailListType}} alerts.
                </p>
                <div style="display: flex; justify-content: center; gap: 24px;">
                    <a href="{{managePreferencesLink}}" style="font-family: 'Open Sans', Arial, sans-serif; font-size: 12px; font-weight: 400; line-height: 16px; color: #ffffff; text-decoration: underline;">
                        Manage Preferences
                    </a>
                    <a href="{{unsubscribeLink}}" style="font-family: 'Open Sans', Arial, sans-serif; font-size: 12px; font-weight: 400; line-height: 16px; color: #ffffff; text-decoration: underline;">
                        Unsubscribe
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Email compatibility notes -->
    <!-- [Variation: For Webinar] Replace {{sectionTitle}} with "Inside the Webinar", {{sectionDescription}} with "Unlock the potential..." -->
    <!-- [Variation: For Meeting] Replace {{sectionTitle}} with "Inside the Meeting", {{sectionDescription}} with "Connect and collaborate..." -->
    <!-- [Variation: For Fundraiser] Replace {{sectionTitle}} with "Making an Impact", {{sectionDescription}} with "Join us in supporting..." -->
    <!--
    Keywords for variations:
    Webinar: sectionTitle: "Inside the Webinar", feature1: "Latest trends in {{topic}}", feature2: "Digital tools", feature3: "Networking opportunities"
    Meeting: sectionTitle: "Meeting Agenda", feature1: "Strategic discussions", feature2: "Community updates", feature3: "Member networking"
    Fundraiser: sectionTitle: "Fundraising Goals", feature1: "Community impact", feature2: "Our objectives", feature3: "Make a difference"
    -->
</body>
</html>