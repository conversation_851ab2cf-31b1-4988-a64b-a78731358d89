import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import { useTranslation } from 'react-i18next';
import i18next from 'i18next';
import '../lib/i18n';
import { EmailFooter } from './components';

/**
 * Agenda item interface definition using the same structure as original
 */
interface AgendaItem {
  time: string;
  description: string;
}

/**
 * CTA Link interface for customizable call-to-action buttons
 */
interface CTALink {
  text: string;
  href: string;
  variant: 'primary' | 'secondary';
}

/**
 * Event invitation V2 email props interface - enhanced version
 */
interface EventInvitationV2Props {
  name?: string;
  clubName?: string;
  eventName?: string;
  eventDate?: string;
  eventTime?: string;
  eventLocation?: string;
  eventDescription?: string;
  bodyText?: string;
  ctaLinks?: CTALink[];
  agenda?: AgendaItem[];
  language?: 'en' | 'fr' | 'ar';
  logoUrl?: string;
  heroImageUrl?: string;
  disclaimerText?: string;
}

/**
 * Rotary Design System V2 for Event Invitation Emails
 * Implements comprehensive Rotary visual identity and guidelines
 * Based on Rotary Style components with email-specific adaptations
 */
export default function EventInvitationV2({
  name = 'Valued Member',
  clubName = 'Tunis Doyen Rotary Club',
  eventName = 'Rotary Service Initiative',
  eventDate = 'September 15, 2025',
  eventTime = '14:00 - 16:00',
  eventLocation = 'Rotary Club Center, Tunis',
  eventDescription = 'Join us for an impactful service initiative that demonstrates Rotary\'s commitment to serving our community.',
  bodyText = 'This event represents Rotary\'s dedication to humanitarian service and fellowship. Your participation will help make a meaningful difference in our community.',
  ctaLinks = [
    {
      text: 'RSVP Now',
      href: 'https://example.com/rsvp',
      variant: 'primary' as const,
    },
    {
      text: 'Add to Calendar',
      href: 'https://calendar.google.com',
      variant: 'secondary' as const,
    },
  ],
  agenda = [],
  language = 'en',
  logoUrl = 'https://res.cloudinary.com/deerc1s7z/image/upload/v1756761035/RotaryMoE-R_CMYK-C_xko6yo.png',
  heroImageUrl = 'https://res.cloudinary.com/deerc1s7z/image/upload/v1736789012/herov2_cropped_down_tzuni7.webp',
  disclaimerText = 'You\'re receiving this email because you\'re a member of our Rotary club. To manage your preferences, click the link in the footer.',
}: EventInvitationV2Props) {
  // Language configuration
  if (typeof window === 'undefined') {
    i18next.changeLanguage(language);
  }

  const { t } = useTranslation();
  const isRTL = language === 'ar';

  // Rotary Color Palette
  const colors = {
    primaryRoyalBlue: '#17458F',
    primaryGold: '#F7A81B',
    white: '#FFFFFF',
    black: '#000000',
    charcoalgrey: '#4C4C4C',
    lightgrey: '#F5F5F5',
  };

  // Get font family based on language
  const getFontFamily = (isArabic: boolean) => {
    return isArabic
      ? '"Tajawal", Arial, sans-serif'
      : '"Open Sans", Arial, Helvetica, sans-serif';
  };

  const currentFontFamily = getFontFamily(isRTL);

  // Email-optimized inline styles - Rotary Design System V2
  const emailStyles: Record<string, any> = {
    // Base layout
    main: {
      backgroundColor: colors.lightgrey,
      fontFamily: currentFontFamily,
      margin: '0',
      padding: '0',
      fontSize: '16px',
      lineHeight: '1.5',
      color: colors.charcoalgrey,
      direction: isRTL ? 'rtl' : 'ltr',
      textAlign: isRTL ? 'right' : 'left',
    },

    container: {
      maxWidth: '600px',
      margin: '0 auto',
      backgroundColor: colors.white,
      fontFamily: currentFontFamily,
      borderRadius: '8px',
      overflow: 'hidden',
      boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
    },

    // Header section - Rotary branding
    headerSection: {
      background: `linear-gradient(135deg, ${colors.primaryRoyalBlue} 0%, #0f3472 100%)`,
      padding: '32px 24px',
      textAlign: 'center' as const,
      position: 'relative' as const,
    },

    logoContainer: {
      position: 'relative' as const,
      zIndex: 2,
    },

    logo: {
      width: '140px',
      height: 'auto',
      maxWidth: '100%',
      display: 'block',
      margin: '0 auto',
    },

    headerText: {
      fontSize: isRTL ? '20px' : '18px',
      color: colors.white,
      margin: '16px 0 0 0',
      fontWeight: '600',
      lineHeight: '1.4',
      fontFamily: currentFontFamily,
      textShadow: '0 1px 2px rgba(0,0,0,0.3)',
    },

    heroGradient: {
      position: 'absolute' as const,
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'linear-gradient(135deg, rgba(23, 69, 143, 0.9) 0%, rgba(15, 52, 114, 0.95) 100%)',
      zIndex: 1,
    },

    // Hero section
    heroSection: {
      background: `linear-gradient(135deg, ${colors.primaryRoyalBlue} 0%, #0f3472 100%)`,
      padding: '40px 24px',
      textAlign: 'center' as const,
      position: 'relative' as const,
    },

    heroImage: {
      width: '100%',
      maxWidth: '480px',
      height: 'auto',
      margin: '0 auto 24px auto',
      borderRadius: '12px',
      display: 'block',
      boxShadow: '0 8px 24px rgba(0,0,0,0.2)',
    },

    // Content sections
    section: {
      padding: '32px 24px',
      fontFamily: currentFontFamily,
    },

    sectionWithBackground: {
      padding: '32px 24px',
      backgroundColor: '#f8fafc',
      fontFamily: currentFontFamily,
    },

    sectionHeading: {
      fontSize: isRTL ? '24px' : '22px',
      fontWeight: '700',
      color: colors.primaryRoyalBlue,
      margin: '0 0 20px 0',
      paddingBottom: '16px',
      borderBottom: `4px solid ${colors.primaryGold}`,
      fontFamily: currentFontFamily,
      lineHeight: '1.3',
    },

    eventDetailsContainer: {
      backgroundColor: '#fafbfc',
      border: `2px solid ${colors.primaryGold}`,
      borderRadius: '12px',
      padding: '24px',
      margin: '24px 0',
      fontFamily: currentFontFamily,
    },

    detailRow: {
      display: 'table',
      width: '100%',
      margin: '12px 0',
      fontFamily: currentFontFamily,
    },

    detailLabel: {
      display: 'table-cell',
      fontWeight: '700',
      color: colors.primaryRoyalBlue,
      paddingRight: '16px',
      width: '120px',
      verticalAlign: 'top',
      fontFamily: currentFontFamily,
    },

    detailValue: {
      display: 'table-cell',
      color: colors.charcoalgrey,
      fontWeight: '400',
      verticalAlign: 'top',
      fontFamily: currentFontFamily,
    },

    // Typography hierarchy
    h1: {
      fontSize: isRTL ? '36px' : '32px',
      fontWeight: '700',
      fontFamily: currentFontFamily,
      color: colors.primaryRoyalBlue,
      lineHeight: '1.2',
      margin: '0 0 16px 0',
    },

    h2: {
      fontSize: isRTL ? '28px' : '24px',
      fontWeight: '600',
      fontFamily: currentFontFamily,
      color: colors.primaryRoyalBlue,
      lineHeight: '1.3',
      margin: '0 0 16px 0',
    },

    h3: {
      fontSize: isRTL ? '22px' : '20px',
      fontWeight: '600',
      fontFamily: currentFontFamily,
      color: colors.primaryRoyalBlue,
      lineHeight: '1.4',
      margin: '0 0 12px 0',
    },

    bodyText: {
      fontSize: '16px',
      fontWeight: '400',
      fontFamily: currentFontFamily,
      color: colors.charcoalgrey,
      lineHeight: '1.6',
      margin: '16px 0',
    },

    // CTA Buttons
    buttonPrimary: {
      backgroundColor: colors.primaryRoyalBlue,
      color: colors.white,
      border: 'none',
      borderRadius: '8px',
      padding: '16px 32px',
      fontSize: '16px',
      fontWeight: '600',
      fontFamily: currentFontFamily,
      textDecoration: 'none',
      textAlign: 'center' as const,
      display: 'inline-block',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      boxShadow: `0 4px 12px rgba(23, 69, 143, 0.3)`,
      margin: '8px',
      minWidth: '180px',
    } as React.CSSProperties,

    buttonSecondary: {
      backgroundColor: colors.primaryGold,
      color: colors.white,
      border: 'none',
      borderRadius: '8px',
      padding: '12px 24px',
      fontSize: '14px',
      fontWeight: '600',
      fontFamily: currentFontFamily,
      textDecoration: 'none',
      textAlign: 'center' as const,
      display: 'inline-block',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      boxShadow: `0 4px 12px rgba(247, 168, 27, 0.3)`,
      margin: '8px',
      minWidth: '150px',
    } as React.CSSProperties,

    buttonContainer: {
      textAlign: 'center' as const,
      padding: '24px 0',
    },

    // Agenda table - responsive table structure
    agendaTable: {
      width: '100%',
      borderCollapse: 'collapse' as const,
      margin: '20px 0',
      borderRadius: '8px',
      overflow: 'hidden',
      boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
    },

    agendaRow: {
      borderBottom: '1px solid #e2e8f0',
    },

    agendaCellTime: {
      backgroundColor: colors.primaryRoyalBlue,
      color: colors.white,
      fontWeight: '600',
      padding: '14px 16px',
      border: 'none',
      width: '140px',
      textAlign: isRTL ? 'right' as const : 'left' as const,
      fontFamily: currentFontFamily,
    },

    agendaCellDescription: {
      backgroundColor: colors.white,
      color: colors.charcoalgrey,
      padding: '14px 16px',
      border: 'none',
      fontFamily: currentFontFamily,
      lineHeight: '1.4',
    },

    // Footer
    footerSection: {
      background: `linear-gradient(135deg, ${colors.primaryRoyalBlue} 0%, #0f3472 100%)`,
      padding: '32px 24px',
      textAlign: 'center' as const,
      color: colors.white,
      fontFamily: currentFontFamily,
    },

    disclaimerText: {
      fontSize: '12px',
      color: '#b3b3b3',
      margin: '16px 0 0 0',
      lineHeight: '1.4',
      fontFamily: currentFontFamily,
    },
  };

  const generateEventCalendarUrl = () => {
    // Generate calendar URL with event details
    const startTime = '20250915T140000Z';
    const endTime = '20250915T160000Z';
    return `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(eventName)}&dates=${startTime}/${endTime}&location=${encodeURIComponent(eventLocation)}&details=${encodeURIComponent(eventDescription)}`;
  };

  const enhancedCtaLinks = ctaLinks.map(link => ({
    ...link,
    href: link.text.includes('Calendar') ? generateEventCalendarUrl() : link.href,
  }));

  return (
    <Html dir={isRTL ? 'rtl' : 'ltr'} lang={language}>
      <Head>
        <title>Rotary Event Invitation</title>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="x-apple-disable-message-reformatting" />
        <meta name="color-scheme" content="light" />
        <meta name="supported-color-schemes" content="light" />
        <style
          dangerouslySetInnerHTML={{
            __html: `
              <!------ Outlook-specific conditional comments ------>
              <!--[if mso]>
              <noscript>
                <xml>
                  <o:OfficeDocumentSettings>
                    <o:AllowPNG />
                    <o:PixelsPerInch>96</o:PixelsPerInch>
                  </o:OfficeDocumentSettings>
                </xml>
              </noscript>
              <![endif]-->

              @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap');
              @import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700&display=swap');

              * {
                box-sizing: border-box;
              }

              body {
                margin: 0;
                padding: 0;
                background-color: ${colors.lightgrey} !important;
              }

              .container {
                max-width: 600px !important;
                margin: 0 auto;
                background-color: ${colors.white} !important;
              }

              .agenda-table {
                width: 100% !important;
                border-collapse: collapse;
              }

              .agenda-table td {
                padding: 14px 16px !important;
                border: none;
              }

              .agenda-time {
                background-color: ${colors.primaryRoyalBlue} !important;
                color: ${colors.white} !important;
                font-weight: 600;
                width: 140px;
              }

              .agenda-description {
                background-color: ${colors.white} !important;
                color: ${colors.charcoalgrey} !important;
                line-height: 1.4;
              }

              .button-primary {
                background-color: ${colors.primaryRoyalBlue} !important;
                color: ${colors.white} !important;
                border: none !important;
                border-radius: 8px !important;
                padding: 16px 32px !important;
                font-size: 16px !important;
                font-weight: 600 !important;
                text-decoration: none !important;
                display: inline-block !important;
                margin: 8px !important;
                min-width: 180px !important;
                box-shadow: 0 4px 12px rgba(23, 69, 143, 0.3) !important;
              }

              .button-secondary {
                background-color: ${colors.primaryGold} !important;
                color: ${colors.white} !important;
                border: none !important;
                border-radius: 8px !important;
                padding: 12px 24px !important;
                font-size: 14px !important;
                font-weight: 600 !important;
                text-decoration: none !important;
                display: inline-block !important;
                margin: 8px !important;
                min-width: 150px !important;
                box-shadow: 0 4px 12px rgba(247, 168, 27, 0.3) !important;
              }

              /* Mobile responsive rules */
              @media only screen and (max-width: 600px) {
                .container { width: 100% !important; }
                .section { padding: 20px 16px !important; }
                .detail-row { display: block !important; }
                .detail-label { display: block !important; padding-bottom: 4px !important; }
                .detail-value { display: block !important; padding-left: 0 !important; }
                .agenda-table { font-size: 14px !important; }
                .agenda-time { width: 100px !important; font-size: 12px !important; }
                .button-primary, .button-secondary { width: 100% !important; margin: 8px 0 !important; }
                .section-heading { font-size: 20px !important; }
                .h1 { font-size: 28px !important; }
                .h2 { font-size: 22px !important; }
              }

              /* Outlook-specific fixes */
              @media screen and (-webkit-min-device-pixel-ratio: 0) {
                .container { width: 600px !important; }
              }
            `,
          }}
        />
      </Head>

      <Body style={emailStyles.main}>
        <Preview>
          {t('previewText', 'Join us for an impactful Rotary event:')} {eventName}
        </Preview>

        <Container style={emailStyles.container}>
          {/* Header Section with Rotary Branding */}
          <Section style={emailStyles.headerSection}>
            <div style={emailStyles.logoContainer}>
              <Img
                src={logoUrl}
                alt={t('logoAlt', 'Rotary Club Logo')}
                style={emailStyles.logo}
              />
            </div>
            <Text style={emailStyles.headerText}>
              Service Above Self
            </Text>
            <div style={emailStyles.heroGradient} />
          </Section>

          {/* Hero Section */}
          <Section style={emailStyles.heroSection}>
            <Img
              src={heroImageUrl}
              alt={t('ariaLabels.hero', 'Rotary Event Hero Image')}
              style={emailStyles.heroImage}
            />
            <Text style={{ ...emailStyles.bodyText, color: colors.white }}>
              {t('welcomeText', 'Join us for an impactful Rotary event')}
            </Text>
          </Section>

          {/* Main Content */}
          <Section style={emailStyles.sectionWithBackground}>
            {/* Personalized Greeting */}
            <Text style={emailStyles.bodyText}>
              {t('greeting', 'Dear')} {name},
            </Text>
            <Text style={emailStyles.bodyText}>
              We are {clubName}. {bodyText}
            </Text>

            {/* Event Details Card */}
            <div style={emailStyles.eventDetailsContainer} role="region" aria-label={t('section.eventDetails', 'Event Details')}>
              <Heading style={emailStyles.h1} role="heading" aria-level={2}>
                {eventName}
              </Heading>

              <div style={emailStyles.detailRow}>
                <div style={emailStyles.detailLabel}>{t('labels.date', 'Date')}:</div>
                <div style={emailStyles.detailValue}>{eventDate}</div>
              </div>

              <div style={emailStyles.detailRow}>
                <div style={emailStyles.detailLabel}>{t('labels.time', 'Time')}:</div>
                <div style={emailStyles.detailValue}>{eventTime}</div>
              </div>

              <div style={emailStyles.detailRow}>
                <div style={emailStyles.detailLabel}>{t('labels.location', 'Location')}:</div>
                <div style={emailStyles.detailValue}>{eventLocation}</div>
              </div>

              <Text style={emailStyles.bodyText}>
                {eventDescription}
              </Text>
            </div>

            {/* CTA Buttons */}
            <div style={emailStyles.buttonContainer}>
              {enhancedCtaLinks.map((cta, index) => (
                <Link
                  key={index}
                  href={cta.href}
                  style={cta.variant === 'primary' ? emailStyles.buttonPrimary : emailStyles.buttonSecondary}
                  target="_blank"
                  className={cta.variant === 'primary' ? 'button-primary' : 'button-secondary'}
                >
                  {cta.text}
                </Link>
              ))}
            </div>
          </Section>

          {agenda && agenda.length > 0 && (
            <Section style={emailStyles.section}>
              <Heading style={emailStyles.sectionHeading} role="heading" aria-level={3}>
                {t('labels.agenda', 'Agenda')}
              </Heading>

              <table style={emailStyles.agendaTable} role="table" aria-label={t('labels.agenda', 'Event Agenda')} className="agenda-table">
                <tbody>
                  {agenda.map((item, index) => (
                    <tr key={index} style={emailStyles.agendaRow}>
                      <td
                        style={emailStyles.agendaCellTime}
                        role="cell"
                        className="agenda-time"
                      >
                        {item.time}
                      </td>
                      <td
                        style={emailStyles.agendaCellDescription}
                        role="cell"
                        className="agenda-description"
                      >
                        {item.description}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </Section>
          )}

          {/* Mission Statement */}
          <Section style={emailStyles.sectionWithBackground}>
            <Heading style={emailStyles.h3} role="heading" aria-level={4}>
              {t('section.mission', 'Our Mission')}
            </Heading>
            <Text style={emailStyles.bodyText}>
              <em style={{ color: colors.primaryGold, fontWeight: 'bold' } as React.CSSProperties}>
                "{t('footer.motto', 'Service Above Self')}"
              </em>
            </Text>
            <Text style={emailStyles.bodyText}>
              {t('missionDescription', 'We are Rotary. We are people of action working together to serve our local and global communities through fellowship and service.')}
            </Text>
          </Section>

          {/* Rotary Footer with Branding */}
          <Section style={emailStyles.footerSection}>
            <div style={{ marginBottom: '20px' } as React.CSSProperties}>
              <Img
                src={logoUrl}
                alt="Rotary Club Logo"
                style={{
                  display: 'block' as const,
                  margin: '0 auto',
                  width: '120px',
                  height: 'auto',
                  maxWidth: '100%',
                }}
              />
            </div>

            <Text style={{ ...emailStyles.bodyText, color: colors.white, margin: '20px 0' }}>
              {t('closing.regards', 'Regards')}<br />
              <strong>{clubName}</strong>
            </Text>

            {disclaimerText && (
              <Text style={emailStyles.disclaimerText}>
                {disclaimerText}
              </Text>
            )}
          </Section>
        </Container>
      </Body>
    </Html>
  );
};