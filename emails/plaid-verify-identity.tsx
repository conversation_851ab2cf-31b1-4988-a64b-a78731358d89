import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Section,
  Text,
} from '@react-email/components';


interface PlaidVerifyIdentityEmailProps {
  validationCode?: string;
  clubName?: string;
}



export const PlaidVerifyIdentityEmail = ({
  validationCode,
  clubName = 'Tunis Doyen Rotary Club',
}: PlaidVerifyIdentityEmailProps) => (
  <Html>
    <Head />
    <Body style={main}>
      <Container style={container}>
        <Img
          src="/static/Rotary_logo.png"
          width="120"
          height="36"
          alt="Tunis Doyen Rotary Club - Service Above Self"
          style={logoImage}
        />
        <Text style={tertiary}>Club Member Verification</Text>
        <Heading style={secondary}>
          Enter the following code to verify your membership with {clubName}.
        </Heading>
        <Section style={codeContainer}>
          <Text style={code}>{validationCode}</Text>
        </Section>
        <Text style={paragraph}>Not expecting this email?</Text>
        <Text style={paragraph}>
          Contact{' '}
          <Link href="mailto:<EMAIL>" style={link}>
            <EMAIL>
          </Link>{' '}
          if you did not request this code.
        </Text>
      </Container>
      <Text style={footer}>Securely powered by Plaid.</Text>
    </Body>
  </Html>
);

PlaidVerifyIdentityEmail.PreviewProps = {
  validationCode: '144833',
} as PlaidVerifyIdentityEmailProps;

export default PlaidVerifyIdentityEmail;

const main = {
  backgroundColor: '#ffffff',
  fontFamily: '"Open Sans", Arial, Helvetica, sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  border: `1px solid #e2e8f0`,
  borderRadius: '4px',
  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
  marginTop: '20px',
  maxWidth: '360px',
  margin: '0 auto',
  padding: '64px 0 128px',
};



const tertiary = {
  color: '#17458F',
  fontSize: '14px',
  fontWeight: 'bold',
  fontFamily: '"Open Sans", Arial, Helvetica, sans-serif',
  height: '16px',
  letterSpacing: '0',
  lineHeight: '16px',
  margin: '16px 8px 8px 8px',
  textTransform: 'uppercase' as const,
  textAlign: 'center' as const,
};

const secondary = {
  color: '#000000',
  display: 'inline-block',
  fontFamily: '"Open Sans", Arial, Helvetica, sans-serif',
  fontSize: '20px',
  fontWeight: '500',
  lineHeight: '24px',
  marginBottom: '0',
  marginTop: '0',
  textAlign: 'center' as const,
};

const codeContainer = {
  background: '#e2e8f0',
  borderRadius: '4px',
  margin: '16px auto 12px',
  verticalAlign: 'middle',
  width: '280px',
};

const code = {
  color: '#000000',
  display: 'inline-block',
  fontFamily: '"Open Sans", Arial, Helvetica, sans-serif',
  fontSize: '32px',
  fontWeight: 'bold',
  letterSpacing: '6px',
  lineHeight: '40px',
  paddingBottom: '8px',
  paddingTop: '8px',
  margin: '0 auto',
  width: '100%',
  textAlign: 'center' as const,
};

const paragraph = {
  color: '#374151',
  fontSize: '16px',
  fontFamily: '"Open Sans", Arial, Helvetica, sans-serif',
  letterSpacing: '0',
  lineHeight: '23px',
  padding: '0 40px',
  margin: '0',
  textAlign: 'center' as const,
};

const link = {
  color: '#374151',
  textDecoration: 'underline',
};

const footer = {
  color: '#000000',
  fontSize: '14px',
  fontWeight: 'bold',
  letterSpacing: '0',
  lineHeight: '23px',
  margin: '0',
  marginTop: '20px',
  fontFamily: '"Open Sans", Arial, Helvetica, sans-serif',
  textAlign: 'center' as const,
  textTransform: 'uppercase' as const,
};

const logoImage = {
  maxWidth: '100%',
  height: 'auto',
  display: 'block',
  margin: '0 auto',
};
