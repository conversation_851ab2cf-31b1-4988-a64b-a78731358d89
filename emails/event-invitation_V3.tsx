/**
 * EventInvitationV3 - Token-Compliant Email Template Component
 *
 * A fully styled React email template for Rotary International event invitations
 * that supports external override via props while maintaining rotary design consistency.
 *
 * ✅ Features:
 * - Full TOKENS integration for design system compliance
 * - Email-compatible inline styles (no Tailwind dependencies)
 * - Responsive design with grid layouts
 * - Accessibility features (ARIA labels, alt text)
 * - Error handling for invalid props
 * - TypeScript support with proper interfaces
 * - Google Fonts integration for better typography
 *
 * 🎯 Event Types Supported:
 * - "webinar" - Digital events with networking focus
 * - "club" - Local club meetings with community discussions
 * - "fundraiser" - Charity events with impact stories
 *
 * 📧 Email Compatibility:
 * - Inline styles for maximum email client support
 * - Table-free responsive layouts
 * - Fallback font stacking
 * - Optimized image handling
 */

import React, { CSSProperties } from 'react';
import { TOKENS } from '../lib/tokens';

interface EventInvitationV3Props {
  /** Defines the event type which changes content, icons, and styling */
  eventType?: "webinar" | "club" | "fundraiser";

  /** Optional data overrides for customization */
  eventData?: {
    /** Custom event title override */
    title?: string;
    /** Custom event description override */
    description?: string;
    /** Custom date override (defaults to "October 15, 2024") */
    date?: string;
    /** Custom time override (defaults to "2:00 PM EST") */
    time?: string;
    /** Custom speakers array override */
    speakers?: Array<{
      name: string;
      title: string;
      bio: string;
      image: string;
    }>;
  };
}

const EventInvitationV3: React.FC<EventInvitationV3Props> = ({
  eventType = "webinar",
  eventData
}) => {
  // Error handling for required props
  if (!eventType || !['webinar', 'club', 'fundraiser'].includes(eventType)) {
    console.error('EventInvitationV3: Invalid eventType provided');
    return null;
  }

  // Define branding constants
  const logoText = "Rotary International";
  const headerSubtitle = "Service Above Self";

  // Define defaults with working links
  const eventDate = "October 15, 2024";
  const eventTime = "2:00 PM EST";
  const ctaText = "Register Now";
  const ctaUrl = "https://rotarytunis.site/events/register";
  const rsvpQuestion = "Will you join us?";
  const rsvpYes = "Yes, I'm Attending";
  const rsvpYesUrl = "https://rotarytunis.site/events/rsvp-yes";
  const rsvpNo = "No, Thanks";
  const rsvpNoUrl = "mailto:<EMAIL>?subject=Cannot%20attend%20event";
  const footerLinks = "Contact Us | Privacy Policy";
  const footerText = "© 2025 Rotary Tunis Doyen. All rights reserved.";
  const contactUrl = "https://rotarytunis.site/contact";
  const privacyUrl = "https://rotarytunis.site/privacy";
  const calendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=Rotary%20Event&dates=20241015T180000Z/20241015T200000Z&location=Tunis%2C%20Tunisia&details=Join%20us%20for%20an%20impactful%20Rotary%20event`;
  const managePreferencesLink = "https://rotarytunis.site/preferences";
  const unsubscribeLink = "https://rotarytunis.site/unsubscribe";

  // Default dynamic content
  let eventTitle = "Join Our Live Webinar: Digital Marketing Breakthroughs!";
  let eventDescription = "Discover cutting-edge strategies and virtual networking opportunities to advance your skills and connect with industry leaders.";
  let heroImageUrl = "https://via.placeholder.com/504x322?text=Event+Hero+Image";
  let contentTitle = "Why Attend This Webinar?";
  let contentDescription = "Gain expert insights from seasoned professionals through interactive sessions and exclusive content tailored for growth and networking.";
  const highlightText = "Key Highlights";
  const speakersTitle = "Featured Speakers";
  let featureIcon1 = "https://via.placeholder.com/35x35?text=Trends";
  let featureText1 = "Latest Digital Marketing Trends";
  let featureIcon2 = "https://via.placeholder.com/35x35?text=Tools";
  let featureText2 = "Essential Digital Tools & Strategies";
  let featureIcon3 = "https://via.placeholder.com/35x35?text=Network";
  let featureText3 = "Virtual Networking Opportunities";

  switch (eventType) {
    case "club":
      eventTitle = "Weekly Club Meeting: Community Service Planning";
      eventDescription = "Join fellow members to plan impactful community service initiatives and build stronger local connections.";
      heroImageUrl = "https://via.placeholder.com/504x322?text=Club+Meeting+Hero+Image";
      contentTitle = "Meeting Agenda";
      contentDescription = "Engage in strategic discussions, receive community updates, and network with dedicated local members.";
      featureIcon1 = "https://via.placeholder.com/35x35?text=Discuss";
      featureText1 = "Strategic Community Discussions";
      featureIcon2 = "https://via.placeholder.com/35x35?text=Updates";
      featureText2 = "Local Community Updates";
      featureIcon3 = "https://via.placeholder.com/35x35?text=Network";
      featureText3 = "Member Networking Session";
      break;
    case "fundraiser":
      eventTitle = "Annual Charity Gala: Making a Difference Together";
      eventDescription = "Celebrate our achievements and support vital causes through entertaining activities and community involvement.";
      heroImageUrl = "https://via.placeholder.com/504x322?text=Fundraiser+Gala+Hero+Image";
      contentTitle = "Support Our Cause";
      contentDescription = "Contribute to meaningful initiatives, enjoy special entertainment, and connect with fellow supporters.";
      featureIcon1 = "https://via.placeholder.com/35x35?text=Impact";
      featureText1 = "Community Impact Stories";
      featureIcon2 = "https://via.placeholder.com/35x35?text=Opps";
      featureText2 = "Volunteer Opportunities";
      featureIcon3 = "https://via.placeholder.com/35x35?text=Networking";
      featureText3 = "Social Networking";
      break;
    // default is webinar, already set above
  }

  // Icon constants - using SVG data URIs for better email compatibility
  const calendarIcon = `data:image/svg+xml;base64,${btoa(`<svg width="46" height="43" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 2V5M16 2V5M3.5 9.09H20.5M21 8.5V17C21 20 19.5 22 16 22H8C4.5 22 3 20 3 17V8.5C3 5.5 4.5 3.5 8 3.5H16C19.5 3.5 21 5.5 21 8.5Z" stroke="#17458F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M11.9955 13.7H12.0045M15.2953 13.7H15.3043M8.79416 17.2H8.80316" stroke="#17458F" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>`)}`;

  const clockIcon = `data:image/svg+xml;base64,${btoa(`<svg width="43" height="43" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M22 12C22 17.52 17.52 22 12 22C6.48 22 2 17.52 2 12C2 6.48 6.48 2 12 2C17.52 2 22 6.48 22 12Z" stroke="#17458F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
    <path d="M15 12H12C11.45 12 11 11.55 11 11V8" stroke="#17458F" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
  </svg>`)}`;

  // Social media icons
  const facebookIcon = `data:image/svg+xml;base64,${btoa(`<svg width="23" height="23" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M24 12.073C24 5.405 18.627 0 12 0S0 5.405 0 12.073C0 17.966 4.293 22.9 9.914 23.834V15.373H6.957V12.073H9.914V9.764C9.914 6.848 11.662 5.19 14.385 5.19C15.618 5.19 16.895 5.41 16.895 5.41V8.33H15.473C14.08 8.33 13.643 9.192 13.643 10.081V12.073H16.77L16.244 15.373H13.643V23.834C19.264 22.9 24 17.966 24 12.073Z" fill="#FFFFFF"/>
  </svg>`)}`;

  const twitterIcon = `data:image/svg+xml;base64,${btoa(`<svg width="23" height="23" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M23.953 4.57C23.072 4.96 22.124 5.209 21.142 5.308C22.152 4.702 22.927 3.732 23.278 2.57C22.34 3.126 21.301 3.522 20.206 3.723C19.371 2.847 18.113 2.28 16.751 2.28C13.923 2.28 11.617 4.586 11.617 7.414C11.617 7.813 11.662 8.2 11.747 8.567C7.731 8.367 4.132 6.437 1.684 3.275C1.243 4.03 0.983 4.915 0.983 5.855C0.983 7.587 1.909 9.141 3.286 10.02C2.479 9.995 1.712 9.774 1.026 9.4V9.467C1.026 11.702 2.616 14.513 4.645 14.98C4.222 15.094 3.777 15.152 3.324 15.152C2.975 15.152 2.635 15.119 2.301 15.057C2.99 17.227 4.971 18.764 7.324 18.804C5.359 20.349 2.841 21.282 0.103 21.282C-0.302 21.282 -0.706 21.258 -1.104 21.228C3.298 22.833 7.172 23.75 11.245 23.75C16.696 23.75 20.967 19.316 20.967 7.875C20.967 7.736 20.964 7.598 20.957 7.46C21.827 6.829 22.589 6.066 23.278 5.25C22.472 5.603 21.609 5.84 20.71 5.95C21.633 5.294 22.399 4.455 22.953 3.455C22.103 3.787 21.204 4.01 20.27 4.11C19.471 3.285 18.428 2.764 17.268 2.764C15.065 2.764 13.263 4.566 13.263 6.769C13.263 7.055 13.295 7.334 13.358 7.605C9.42 7.408 5.863 5.524 3.537 2.374C2.831 3.584 2.46 5.003 2.46 6.49C2.46 7.174 2.553 7.838 2.731 8.474C8.213 8.474 13.048 13.283 13.048 18.764V19.641C13.048 20.959 15.72 22.127 15.72 22.127C15.72 22.127 16.386 21.715 16.943 21.287C12.26 21.928 7.14 21.928 2.456 21.287C3.013 21.715 3.679 22.127 3.679 22.127C3.679 22.127 6.351 22.127 6.351 20.959V18.764C6.351 13.512 10.21 9.229 15.043 8.474C15.107 8.199 15.139 7.92 15.139 7.635C15.139 4.273 17.042 1.508 19.357 1.508C20.364 1.508 21.294 1.847 22.031 2.447C23.057 1.353 24.127 0.36 25.258 0.36C24.737 2.144 23.658 3.656 22.237 4.611C23.544 4.457 24.8 4.101 25.964 3.573C25.509 4.86 24.712 6.001 23.643 6.807Z" fill="#FFFFFF"/>
  </svg>`)}`;

  const linkedinIcon = `data:image/svg+xml;base64,${btoa(`<svg width="23" height="23" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20.447 20.452H16.893V14.882C16.893 13.556 16.637 12.421 15.178 12.421C13.698 12.421 13.405 13.36 13.405 14.811V20.452H9.858V8.791H13.277V10.414C13.731 9.518 14.756 8.603 16.433 8.603C19.031 8.603 20.447 10.304 20.447 14.302V20.452ZM5.337 7.433C4.236 7.433 3.343 6.547 3.343 5.447C3.343 4.347 4.236 3.454 5.337 3.454C6.438 3.454 7.331 4.347 7.331 5.447C7.331 6.547 6.438 7.433 5.337 7.433ZM7.002 20.452H3.48V8.791H7.002V20.452ZM22.225 0.09H1.771C0.794 0.09 0 0.883 0 1.859V22.226C0 23.202 0.794 24 1.771 24H22.225C23.202 24 24 23.202 24 22.226V1.859C24 0.883 23.202 0.09 22.225 0.09Z" fill="#FFFFFF"/>
  </svg>`)}`;

  // Speaker data
  const speakers = [
    {
      name: "John Smith",
      title: "Community Leader",
      bio: "20 years of service in local community projects, specializing in youth outreach programs.",
      image: "https://via.placeholder.com/77x77?text=Speaker+1"
    },
    {
      name: "Jane Doe",
      title: "Program Coordinator",
      bio: "Expert in logistics and coordination for large-scale community events and initiatives.",
      image: "https://via.placeholder.com/77x77?text=Speaker+2"
    },
    {
      name: "Michael Johnson",
      title: "Fundraiser Specialist",
      bio: "Dedicated to securing funding for charitable causes across multiple regions.",
      image: "https://via.placeholder.com/77x77?text=Speaker+3"
    }
  ];

  return (
    <>
      <style
        dangerouslySetInnerHTML={{
          __html: "@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700&display=swap');"
        }}
      />
      <div style={{
        maxWidth: '600px',
        margin: '0 auto',
        backgroundColor: TOKENS.color.neutral.white,
        position: 'relative',
        fontFamily: TOKENS.typography.family.primary
      }}>
        {/* Header */}
        <div style={{
          backgroundColor: TOKENS.color.brand.royalBlue,
          height: '66px',
          width: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
        }}>
          <div style={{
            fontFamily: TOKENS.typography.family.primary,
            fontSize: TOKENS.typography.scale.h2,
            fontWeight: TOKENS.typography.weights.bold,
            lineHeight: '2rem',
            color: TOKENS.color.neutral.white
          }}>
            {logoText}
          </div>
        </div>

        {/* Header Gradient */}
        <div style={{
          background: `linear-gradient(to bottom, ${TOKENS.color.brand.royalBlue} 0%, ${TOKENS.color.brand.royalBlueDark} 100%)`,
          height: '25px',
          width: '100%'
        }} />

        {/* Header Subtitle */}
        <div style={{
          backgroundColor: TOKENS.color.neutral.smoke,
          padding: `${TOKENS.spacing.scale[2]} ${TOKENS.spacing.scale[4]}`
        }}>
          <p style={{
            textAlign: 'center',
            fontSize: TOKENS.typography.scale.small,
            fontWeight: TOKENS.typography.weights.regular,
            lineHeight: '1.25rem',
            color: TOKENS.color.brand.royalBlue,
            margin: '0'
          }}>
            {headerSubtitle}
          </p>
        </div>

        {/* Hero Image */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          padding: `0 ${TOKENS.spacing.scale[5]} ${TOKENS.spacing.scale[1]} ${TOKENS.spacing.scale[5]}`
        }}>
          <div style={{
            backgroundImage: `url('${heroImageUrl}')`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            backgroundColor: TOKENS.color.neutral.smoke,
            height: '322px',
            width: '504px',
            borderRadius: TOKENS.radius.md,
          }}>
            {/* Alt text for accessibility */}
            <span role="img" aria-label={`Hero image for ${eventType} event`} />
          </div>
        </div>

        {/* Event Title Section */}
        <div style={{
          backgroundColor: `${TOKENS.color.brand.royalBlue}26`, // 15% opacity
          padding: `${TOKENS.spacing.scale[3]} ${TOKENS.spacing.scale[5]}`
        }}>
          <h1 style={{
            fontFamily: TOKENS.typography.family.primary,
            fontSize: TOKENS.typography.scale.h2,
            fontWeight: TOKENS.typography.weights.bold,
            lineHeight: '2.125rem',
            color: TOKENS.color.brand.royalBlue,
            margin: '0',
            textAlign: 'center'
          }}>
            {eventTitle}
          </h1>
        </div>

        {/* Event Description */}
        <div style={{
          padding: `${TOKENS.spacing.scale[3]} ${TOKENS.spacing.scale[5]}`
        }}>
          <p style={{
            fontFamily: TOKENS.typography.family.primary,
            fontSize: TOKENS.typography.scale.body,
            fontWeight: TOKENS.typography.weights.regular,
            lineHeight: '1.5rem',
            color: TOKENS.color.neutral.nearBlack,
            margin: '0',
            maxWidth: '422px',
            marginLeft: 'auto',
            marginRight: 'auto',
            textAlign: 'center'
          }}>
            {eventDescription}
          </p>
        </div>

        {/* CTA Buttons */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          gap: TOKENS.spacing.scale[2],
          paddingBottom: `${TOKENS.spacing.scale[2]}`
        }}>
          <a
            href={ctaUrl}
            style={{
              display: 'inline-block',
              padding: `${TOKENS.spacing.scale[2]} ${TOKENS.spacing.scale[4]}`,
              backgroundColor: TOKENS.color.brand.royalBlue,
              color: TOKENS.color.neutral.white,
              borderRadius: TOKENS.radius.sm,
              textAlign: 'center',
              fontFamily: TOKENS.typography.family.primary,
              fontSize: TOKENS.typography.scale.body,
              fontWeight: TOKENS.typography.weights.medium,
              textDecoration: 'none',
            }}
          >
            {ctaText}
          </a>
          <a
            href={calendarUrl}
            target="_blank"
            style={{
              display: 'inline-block',
              padding: `${TOKENS.spacing.scale[2]} ${TOKENS.spacing.scale[4]}`,
              backgroundColor: TOKENS.color.brand.gold,
              color: TOKENS.color.neutral.white,
              borderRadius: TOKENS.radius.sm,
              textAlign: 'center',
              fontFamily: TOKENS.typography.family.primary,
              fontSize: TOKENS.typography.scale.body,
              fontWeight: TOKENS.typography.weights.medium,
              textDecoration: 'none',
            }}
          >
            Add to Calendar
          </a>
        </div>

        {/* Event Details */}
        <div style={{
          backgroundColor: TOKENS.color.neutral.smoke,
          padding: TOKENS.spacing.scale[3]
        }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <div style={{
              display: 'flex',
              alignItems: 'center',
              gap: TOKENS.spacing.scale[4],
              textAlign: 'center'
            }}>
              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: TOKENS.spacing.scale[1]
              }}>
                <div style={{
                  width: '46px',
                  height: '43px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <img src={calendarIcon} alt="Calendar icon" style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain'
                  }} />
                </div>
                <div>
                  <p style={{
                    fontFamily: TOKENS.typography.family.primary,
                    fontSize: TOKENS.typography.scale.small,
                    fontWeight: TOKENS.typography.weights.medium,
                    lineHeight: '1.25rem',
                    color: TOKENS.color.neutral.nearBlack,
                    margin: '0'
                  }}>DATE:</p>
                  <p style={{
                    fontFamily: TOKENS.typography.family.primary,
                    fontSize: TOKENS.typography.scale.small,
                    fontWeight: TOKENS.typography.weights.regular,
                    lineHeight: '1.25rem',
                    color: TOKENS.color.neutral.nearBlack,
                    margin: '0'
                  }}>{eventDate}</p>
                </div>
              </div>

              <div style={{
                width: '1px',
                height: '34px',
                backgroundColor: TOKENS.color.neutral.slate
              }} />

              <div style={{
                display: 'flex',
                alignItems: 'center',
                gap: TOKENS.spacing.scale[1]
              }}>
                <div style={{
                  width: '43px',
                  height: '43px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}>
                  <img src={clockIcon} alt="Clock icon" style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain'
                  }} />
                </div>
                <div>
                  <p style={{
                    fontFamily: TOKENS.typography.family.primary,
                    fontSize: TOKENS.typography.scale.small,
                    fontWeight: TOKENS.typography.weights.medium,
                    lineHeight: '1.25rem',
                    color: TOKENS.color.neutral.nearBlack,
                    margin: '0'
                  }}>TIME:</p>
                  <p style={{
                    fontFamily: TOKENS.typography.family.primary,
                    fontSize: TOKENS.typography.scale.small,
                    fontWeight: TOKENS.typography.weights.regular,
                    lineHeight: '1.25rem',
                    color: TOKENS.color.neutral.nearBlack,
                    margin: '0'
                  }}>{eventTime}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Inside the Event Section */}
        <div style={{
          padding: `${TOKENS.spacing.scale[5]} ${TOKENS.spacing.scale[5]}`
        }}>
          <h2 style={{
            fontFamily: TOKENS.typography.family.primary,
            fontSize: TOKENS.typography.scale.h2,
            fontWeight: TOKENS.typography.weights.bold,
            lineHeight: '2.125rem',
            color: TOKENS.color.brand.royalBlue,
            margin: '0 0 1rem 0'
          }}>{contentTitle}</h2>
          <p style={{
            fontFamily: TOKENS.typography.family.primary,
            fontSize: TOKENS.typography.scale.body,
            fontWeight: TOKENS.typography.weights.regular,
            lineHeight: '1.5rem',
            color: TOKENS.color.neutral.nearBlack,
            margin: '0 0 1.5rem 0'
          }}>
            {contentDescription}
          </p>

          <p style={{
            fontFamily: TOKENS.typography.family.primary,
            fontSize: TOKENS.typography.scale.body,
            fontWeight: TOKENS.typography.weights.regular,
            lineHeight: '1.5rem',
            color: TOKENS.color.brand.gold,
            margin: '0 0 1.5rem 0'
          }}>{highlightText}</p>

          <div style={{
            display: 'grid',
            gap: TOKENS.spacing.scale[3],
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))'
          }}>
            <div style={{
              textAlign: 'center'
            }}>
              <div style={{
                width: '32px',
                height: '32px',
                margin: '0 auto 12px auto'
              }}>
                <img src={featureIcon1} alt={`Feature 1 icon for ${eventType}`} style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain'
                }} />
              </div>
              <p style={{
                fontSize: TOKENS.typography.scale.small,
                fontWeight: TOKENS.typography.weights.regular,
                lineHeight: '1.25rem',
                color: TOKENS.color.neutral.charcoal
              }}>
                {featureText1}
              </p>
            </div>

            <div style={{
              textAlign: 'center'
            }}>
              <div style={{
                width: '32px',
                height: '32px',
                margin: '0 auto 12px auto'
              }}>
                <img src={featureIcon2} alt={`Feature 2 icon for ${eventType}`} style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain'
                }} />
              </div>
              <p style={{
                fontSize: TOKENS.typography.scale.small,
                fontWeight: TOKENS.typography.weights.regular,
                lineHeight: '1.25rem',
                color: TOKENS.color.neutral.charcoal
              }}>
                {featureText2}
              </p>
            </div>

            <div style={{
              textAlign: 'center'
            }}>
              <div style={{
                width: '32px',
                height: '32px',
                margin: '0 auto 12px auto'
              }}>
                <img src={featureIcon3} alt={`Feature 3 icon for ${eventType}`} style={{
                  width: '100%',
                  height: '100%',
                  objectFit: 'contain'
                }} />
              </div>
              <p style={{
                fontSize: TOKENS.typography.scale.small,
                fontWeight: TOKENS.typography.weights.regular,
                lineHeight: '1.25rem',
                color: TOKENS.color.neutral.charcoal
              }}>
                {featureText3}
              </p>
            </div>
          </div>
        </div>

        {/* Speakers Section */}
        <div style={{
          padding: `${TOKENS.spacing.scale[5]} ${TOKENS.spacing.scale[5]}`
        }}>
          <h2 style={{
            fontFamily: TOKENS.typography.family.primary,
            fontSize: TOKENS.typography.scale.h2,
            fontWeight: TOKENS.typography.weights.bold,
            lineHeight: '2.125rem',
            color: TOKENS.color.brand.royalBlue,
            margin: '0 0 2rem 0'
          }}>{speakersTitle}</h2>

          <div style={{
            display: 'grid',
            gap: TOKENS.spacing.scale[4],
            gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))'
          }}>
            {speakers.map((speaker, index) => (
              <div key={index} style={{
                textAlign: 'center'
              }}>
                <div style={{
                  width: '80px',
                  height: '80px',
                  margin: '0 auto 1rem auto'
                }}>
                  <img src={speaker.image} alt={`Photo of ${speaker.name}, ${speaker.title}`} style={{
                    width: '100%',
                    height: '100%',
                    borderRadius: '50%',
                    objectFit: 'cover',
                    display: 'block'
                  }} />
                </div>
                <div style={{
                  width: '100%',
                  height: '1px',
                  backgroundColor: TOKENS.color.neutral.slate,
                  marginBottom: '12px'
                }} />
                <h3 style={{
                  fontSize: TOKENS.typography.scale.body,
                  fontWeight: TOKENS.typography.weights.regular,
                  lineHeight: '1.5rem',
                  color: TOKENS.color.neutral.trueBlack,
                  margin: '0 0 0.25rem 0'
                }}>{speaker.name}</h3>
                <p style={{
                  fontSize: TOKENS.typography.scale.small,
                  fontWeight: TOKENS.typography.weights.regular,
                  lineHeight: '1rem',
                  color: TOKENS.color.brand.gold,
                  margin: '0 0 12px 0'
                }}>{speaker.title}</p>
                <p style={{
                  fontSize: TOKENS.typography.scale.small,
                  fontWeight: TOKENS.typography.weights.regular,
                  lineHeight: '1rem',
                  color: TOKENS.color.neutral.charcoal
                }}>{speaker.bio}</p>
              </div>
            ))}
          </div>
        </div>

        {/* RSVP Section */}
        <div style={{
          backgroundColor: TOKENS.color.neutral.smoke,
          padding: TOKENS.spacing.scale[4]
        }}>
          <div style={{
            textAlign: 'center'
          }}>
            <h2 style={{
              fontFamily: TOKENS.typography.family.primary,
              fontSize: TOKENS.typography.scale.h1,
              fontWeight: TOKENS.typography.weights.bold,
              lineHeight: '1.75rem',
              color: TOKENS.color.brand.royalBlue,
              margin: '0 0 1.5rem 0'
            }}>{rsvpQuestion}</h2>
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              gap: TOKENS.spacing.scale[1]
            }}>
              <a href={rsvpYesUrl} style={{
                display: 'inline-block',
                padding: `${TOKENS.spacing.scale[2]} ${TOKENS.spacing.scale[3]}`,
                backgroundColor: TOKENS.color.brand.royalBlue,
                color: TOKENS.color.neutral.white,
                borderRadius: TOKENS.radius.sm,
                textAlign: 'center',
                fontFamily: TOKENS.typography.family.primary,
                fontSize: TOKENS.typography.scale.body,
                fontWeight: TOKENS.typography.weights.medium,
                textDecoration: 'none'
              }}>
                {rsvpYes}
              </a>
              <a href={rsvpNoUrl} style={{
                display: 'inline-block',
                padding: `${TOKENS.spacing.scale[2]} ${TOKENS.spacing.scale[3]}`,
                backgroundColor: TOKENS.color.neutral.white,
                color: TOKENS.color.brand.gold,
                border: `2px solid ${TOKENS.color.brand.gold}`,
                borderRadius: TOKENS.radius.sm,
                textAlign: 'center',
                fontFamily: TOKENS.typography.family.primary,
                fontSize: TOKENS.typography.scale.body,
                fontWeight: TOKENS.typography.weights.medium,
                textDecoration: 'none'
              }}>
                {rsvpNo}
              </a>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div style={{
          backgroundColor: TOKENS.color.brand.royalBlueDark,
          padding: `${TOKENS.spacing.scale[4]} ${TOKENS.spacing.scale[5]}`
        }}>
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            gap: TOKENS.spacing.scale[3],
            marginBottom: TOKENS.spacing.scale[1]
          }}>
            <div style={{
              width: '24px',
              height: '24px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: TOKENS.color.neutral.white,
              borderRadius: TOKENS.radius.pill
            }}>
              <img src={facebookIcon} alt="Rotary International Facebook" style={{
                width: '14px',
                height: '14px',
                objectFit: 'contain'
              }} />
            </div>
            <div style={{
              width: '24px',
              height: '24px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: TOKENS.color.neutral.white,
              borderRadius: TOKENS.radius.pill
            }}>
              <img src={twitterIcon} alt="Rotary International Twitter" style={{
                width: '14px',
                height: '14px',
                objectFit: 'contain'
              }} />
            </div>
            <div style={{
              width: '24px',
              height: '24px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: TOKENS.color.neutral.white,
              borderRadius: TOKENS.radius.pill
            }}>
              <img src={linkedinIcon} alt="Rotary International LinkedIn" style={{
                width: '14px',
                height: '14px',
                objectFit: 'contain'
              }} />
            </div>
          </div>

          <div style={{
            textAlign: 'center',
            color: TOKENS.color.neutral.white
          }}>
            <p style={{
              fontSize: TOKENS.typography.scale.small,
              fontWeight: TOKENS.typography.weights.regular,
              lineHeight: '1.25rem',
              margin: '0 0 0.25rem 0'
            }}>{footerLinks}</p>
            <p style={{
              fontSize: TOKENS.typography.scale.small,
              fontWeight: TOKENS.typography.weights.regular,
              lineHeight: '1.25rem',
              margin: '0 0 1rem 0'
            }}>
              You're receiving this email because you signed up for our {eventType} alerts.
            </p>
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              gap: TOKENS.spacing.scale[3]
            }}>
              <a href={managePreferencesLink} style={{
                fontSize: TOKENS.typography.scale.small,
                fontWeight: TOKENS.typography.weights.regular,
                lineHeight: '1rem',
                color: TOKENS.color.neutral.white,
                textDecoration: 'underline'
              }}>
                Manage Preferences
              </a>
              <a href={unsubscribeLink} style={{
                fontSize: TOKENS.typography.scale.small,
                fontWeight: TOKENS.typography.weights.regular,
                lineHeight: '1rem',
                color: TOKENS.color.neutral.white,
                textDecoration: 'underline'
              }}>
                Unsubscribe
              </a>
            </div>
          </div>
        </div>
    </div>
    </>
  );
};

export default EventInvitationV3;
