import {
  Body,
  <PERSON><PERSON>,
  Container,
  Head,
  Hr,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';




export const StripeWelcomeEmail = () => (
  <Html>
    <Head />
    <Preview>You're now ready to make live transactions with <PERSON><PERSON>!</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={box}>
          <Img
            src="/static/Rotary_logo.png"
            width="120"
            height="36"
            alt="Tunis Doyen Rotary Club - Service Above Self"
            style={logoImage}
          />
          <Hr style={hr} />
          <Text style={paragraph}>
            Welcome to Tunis Doyen Rotary Club! Your membership has been successfully activated.
          </Text>
          <Text style={paragraph}>
            As a Rotarian, you now have access to our member dashboard where you can connect with fellow members, view upcoming events, and contribute to our community service projects.
          </Text>
          <Button style={button} href="https://tunisdoyenrotary.org/dashboard">
            Access Member Dashboard
          </Button>
          <Hr style={hr} />
          <Text style={paragraph}>
            If you haven't finished your integration, you might find our{' '}
            <Link style={anchor} href="https://stripe.com/docs">
              docs
            </Link>{' '}
            handy.
          </Text>
          <Text style={paragraph}>
            Once you're ready to start accepting payments, you'll just need to
            use your live{' '}
            <Link
              style={anchor}
              href="https://dashboard.stripe.com/login?redirect=%2Fapikeys"
            >
              API keys
            </Link>{' '}
            instead of your test API keys. Your account can simultaneously be
            used for both test and live requests, so you can continue testing
            while accepting live payments. Check out our{' '}
            <Link style={anchor} href="https://stripe.com/docs/dashboard">
              tutorial about account basics
            </Link>
            .
          </Text>
          <Text style={paragraph}>
            Finally, we've put together a{' '}
            <Link
              style={anchor}
              href="https://stripe.com/docs/checklist/website"
            >
              quick checklist
            </Link>{' '}
            to ensure your website conforms to card network standards.
          </Text>
          <Text style={paragraph}>
            We'll be here to help you with any step along the way. You can find
            answers to most questions and get in touch with us on our{' '}
            <Link style={anchor} href="https://support.stripe.com/">
              support site
            </Link>
            .
          </Text>
          <Text style={paragraph}>— The Stripe team</Text>
          <Hr style={hr} />
          <Text style={footer}>
            Stripe, 354 Oyster Point Blvd, South San Francisco, CA 94080
          </Text>
        </Section>
      </Container>
    </Body>
  </Html>
);

export default StripeWelcomeEmail;

const main = {
  backgroundColor: '#fcafb9',
  fontFamily: '"Open Sans", Arial, Helvetica, sans-serif',
};

const container = {
  backgroundColor: '#ffffff',
  margin: '0 auto',
  padding: '20px 0 48px',
  marginBottom: '64px',
};

const box = {
  padding: '0 48px',
};

const hr = {
  borderColor: '#e2e8f0',
  margin: '20px 0',
};

const paragraph = {
  color: '#374151',
  fontSize: '16px',
  lineHeight: '24px',
  textAlign: 'left' as const,
};

const anchor = {
  color: '#17458F',
};

const button = {
  backgroundColor: '#17458F',
  borderRadius: '4px',
  color: '#ffffff',
  fontSize: '16px',
  fontWeight: 'bold',
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '100%',
  padding: '8px',
};

const footer = {
  color: '#6b7280',
  fontSize: '14px',
  lineHeight: '16px',
};

const logoImage = {
  maxWidth: '100%',
  height: 'auto',
  display: 'block',
  margin: '0 auto',
};
