/**
 * Rotary Event Invitation Email Template
 * 
 * A clean, professional email template that matches Rotary's brand guidelines
 * and follows the "People of Action" messaging framework.
 */

import React from 'react';
import {
  Html,
  Head,
  Body,
  Container,
  Text,
  Section,
  Preview,
  Img,
} from '@react-email/components';

import { TOKENS } from '../lib/tokens';
import { EmailHeader } from './components/EmailHeader';
import { EventDetailsCard } from './components/EventDetailsCard';
import { CTAButtonGroup } from './components/CTAButtonGroup';
import { EmailFooter } from './components/EmailFooter';

interface RotaryEventInvitationProps {
  eventName?: string;
  eventDate?: string;
  eventTime?: string;
  eventLocation?: string;
  eventDescription?: string;
  heroImageUrl?: string;
  clubName?: string;
  contactEmail?: string;
  websiteUrl?: string;
  rsvpUrl?: string;
  calendarUrl?: string;
}

export const RotaryEventInvitation: React.FC<RotaryEventInvitationProps> = ({
  eventName = 'Rotary Foundation Event',
  eventDate = 'April 20th, 2024',
  eventTime = '6:00 PM',
  eventLocation = 'Community Center',
  eventDescription = 'Join us for an evening of giving back to our community through impactful charitable initiatives.',
  heroImageUrl = 'https://via.placeholder.com/600x300/17458F/FFFFFF?text=Rotary+Foundation',
  clubName = 'Rotary Foundation',
  contactEmail = '<EMAIL>',
  websiteUrl = 'https://rotary.org',
  rsvpUrl = 'mailto:<EMAIL>?subject=RSVP for Rotary Foundation Event',
  calendarUrl = '#'
}) => {
  const containerStyle = {
    margin: '0 auto',
    maxWidth: '600px',
    backgroundColor: TOKENS.color.neutral.white,
    fontFamily: TOKENS.typography.family.primary,
  };

  const heroSectionStyle = {
    padding: '0',
    margin: '0',
  };

  const contentSectionStyle = {
    padding: '32px 24px',
    backgroundColor: TOKENS.color.neutral.white,
  };

  const welcomeTextStyle = {
    fontSize: '16px',
    color: TOKENS.color.neutral.nearBlack,
    lineHeight: 1.6,
    margin: '0 0 24px 0',
    fontFamily: TOKENS.typography.family.primary,
    textAlign: 'center' as const,
  };

  const ctaButtons = [
    {
      text: 'Secure My Spot',
      href: rsvpUrl,
      variant: 'primary' as const,
      ariaLabel: `RSVP for ${eventName}`
    },
    {
      text: 'Add to Calendar',
      href: calendarUrl,
      variant: 'secondary' as const,
      ariaLabel: `Add ${eventName} to calendar`
    }
  ];

  return (
    <Html>
      <Head>
        <meta charSet="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <style dangerouslySetInnerHTML={{ __html: `
          @media only screen and (max-width: 600px) {
            .container { width: 100% !important; }
            .content-section { padding: 20px 16px !important; }
            .hero-image { width: 100% !important; height: auto !important; }
          }
        ` }} />
      </Head>

      <Preview>
        {eventName} - Together, we can change lives and build a better world
      </Preview>

      <Body style={{
        backgroundColor: '#f5f5f5',
        margin: 0,
        padding: '20px 0',
        fontFamily: TOKENS.typography.family.primary,
      }}>
        <Container style={containerStyle} className="container">
          {/* Header */}
          <EmailHeader 
            title={clubName}
            subtitle="Together, we can change lives and build a better world"
          />

          {/* Hero Image */}
          <Section style={heroSectionStyle}>
            <Img
              src={heroImageUrl}
              width={600}
              height={300}
              alt="Rotary Foundation Event"
              style={{
                width: '100%',
                height: 'auto',
                display: 'block',
              }}
              className="hero-image"
            />
          </Section>

          {/* Main Content */}
          <Section style={contentSectionStyle} className="content-section">
            <Text style={welcomeTextStyle}>
              Join us for an evening of giving back to our community through impactful charitable initiatives.
            </Text>

            {/* Event Details Card */}
            <EventDetailsCard
              eventName={eventName}
              eventDate={eventDate}
              eventTime={eventTime}
              eventLocation={eventLocation}
              eventDescription={eventDescription}
            />

            {/* CTA Buttons */}
            <CTAButtonGroup buttons={ctaButtons} />

            {/* Additional Content */}
            <Text style={{
              fontSize: '16px',
              color: TOKENS.color.neutral.nearBlack,
              lineHeight: 1.6,
              margin: '24px 0',
              fontFamily: TOKENS.typography.family.primary,
              textAlign: 'center' as const,
            }}>
              <strong>Will you join us?</strong>
            </Text>

            <Text style={{
              fontSize: '14px',
              color: TOKENS.color.neutral.charcoal,
              lineHeight: 1.5,
              margin: '16px 0',
              fontFamily: TOKENS.typography.family.primary,
            }}>
              Unlock the potential of digital marketing and stay ahead of the curve with our insider knowledge and strategy showcase.
            </Text>
          </Section>

          {/* Footer */}
          <EmailFooter
            clubName={clubName}
            contactEmail={contactEmail}
            websiteUrl={websiteUrl}
          />
        </Container>
      </Body>
    </Html>
  );
};

export default RotaryEventInvitation;