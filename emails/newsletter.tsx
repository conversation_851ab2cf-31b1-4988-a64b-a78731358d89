import {
  Body,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from '@react-email/components';
import { TOKENS } from '../lib/tokens';
import { EmailFooter, emailStyles, CTAButton } from './components';

interface NewsletterEmailProps {
  name?: string;
  clubName?: string;
  projectName?: string;
  projectDescription?: string;
  donationLink?: string;
}

export const NewsletterEmail = ({
  name = '<PERSON><PERSON>',
  clubName = 'Tunis Doyen Rotary Club',
  projectName = 'Clean Water Initiative',
  projectDescription = 'Our latest project focuses on providing clean water to underserved communities in rural Tunisia. We have already installed 15 wells and provided water filtration systems to 300 families.',
  donationLink = 'https://tunisdoyenrotary.org/donate',
}: NewsletterEmailProps) => {
  const previewText = `Latest updates from ${clubName}`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={main}>
        <Container style={container}>
          <Section style={logoContainer}>
            <Img
              src="/static/Rotary_Club_Logo_FR21.png"
              width="120"
              height="36"
              alt="Tunis Doyen Rotary Club - Service Above Self"
              style={logoImage}
            />
          </Section>
          <Heading style={h1}>Latest Updates from {clubName}</Heading>
          <Text style={text}>
            Dear {name},
          </Text>
          <Text style={text}>
            We hope this email finds you well. As Rotarians, we continue our tradition of "Service Above Self" through meaningful projects and community impact. Here are some exciting updates from our club:
          </Text>
          
          <Section style={projectSection}>
            <Heading style={projectTitle}>Project Spotlight: {projectName}</Heading>
            <Img
              src="https://images.unsplash.com/photo-1577896894725-32b0a3b0bd6f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=600&q=80"
              width="520"
              height="240"
              alt={projectName}
              style={projectImage}
            />
            <Text style={text}>
              {projectDescription}
            </Text>
            <Button
              href={donationLink}
              style={button}
            >
              Support This Project
            </Button>
          </Section>
          
          <Section style={upcomingEvents}>
            <Heading style={sectionTitle}>Upcoming Events</Heading>
            <Section style={eventCard}>
              <Text style={eventTitle}>Rotary Foundation Grant Workshop</Text>
              <Text style={eventDate}>September 15, 2023 | 6:00 PM</Text>
              <Text style={eventDescription}>
                Learn how to apply for Rotary Foundation grants to fund your service projects.
              </Text>
            </Section>
            
            <Section style={eventCard}>
              <Text style={eventTitle}>Community Service Day</Text>
              <Text style={eventDate}>September 30, 2023 | 9:00 AM</Text>
              <Text style={eventDescription}>
                Join us for a day of community service at the local park cleanup initiative.
              </Text>
            </Section>
          </Section>
          
          <Section style={membershipSection}>
            <Heading style={sectionTitle}>Membership Spotlight</Heading>
            <Text style={text}>
              We're excited to welcome three new members to our club this month! Please join us in welcoming Dr. Sarah Johnson, Engineer Mohamed Ali, and Teacher Amine Ben Salah.
            </Text>
          </Section>
          
          <Section style={emailStyles.section}>
            <Text style={emailStyles.text}>
              Stay connected with us on social media for daily updates:
            </Text>
            <Section style={emailStyles.center}>
              <Link href="https://facebook.com/tunisdoyenrotary" style={emailStyles.text}>Facebook</Link> |
              <Link href="https://linkedin.com/company/tunis-doyen-rotary" style={emailStyles.text}>LinkedIn</Link>
            </Section>
          </Section>
          
          <Text style={text}>
            Thank you for your continued support of our mission to serve the community and uphold the Rotary motto of "Service Above Self."
          </Text>

          <Section style={emailStyles.section}>
            <Text style={emailStyles.text}>
              Ready to make a difference? Join us in our next community service project.
            </Text>
            <CTAButton
              href="https://rotarytunisdoyen.site/volunteer"
              variant="gold"
            >
              Get Involved
            </CTAButton>
          </Section>

          <Text style={text}>
            Warm regards,
            <br />
            The {clubName} Board
          </Text>

          <EmailFooter />
        </Container>
      </Body>
    </Html>
  );
};

export default NewsletterEmail;

const main = {
  backgroundColor: TOKENS.color.neutral.white,
  fontFamily: TOKENS.typography.family.primary,
};

const container = {
  margin: '0 auto',
  padding: `${TOKENS.spacing.scale[5]} 0 ${TOKENS.spacing.scale[12]}`,
  width: '560px',
};

const h1 = {
  color: TOKENS.color.neutral.nearBlack,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.h3,
  fontWeight: TOKENS.typography.weights.bold,
  margin: `${TOKENS.spacing.scale[10]} 0`,
  padding: '0',
  textAlign: 'center' as const,
};

const logoContainer = {
  textAlign: 'center' as const,
};

const logoImage = {
  maxWidth: '100%',
  height: 'auto',
  display: 'block',
  margin: '0 auto',
};

const text = {
  color: TOKENS.color.neutral.nearBlack,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.body,
  lineHeight: '26px',
};

const button = {
  backgroundColor: TOKENS.color.brand.royalBlue,
  borderRadius: TOKENS.radius.sm,
  color: TOKENS.color.neutral.white,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.body,
  fontWeight: TOKENS.typography.weights.bold,
  textDecoration: 'none',
  textAlign: 'center' as const,
  display: 'block',
  width: '100%',
  padding: `${TOKENS.spacing.scale[3]}`,
};

const projectSection = {
  margin: `${TOKENS.spacing.scale[7]} 0`,
};

const projectTitle = {
  color: TOKENS.color.neutral.nearBlack,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.h3,
  fontWeight: TOKENS.typography.weights.bold,
  margin: `0 0 ${TOKENS.spacing.scale[5]} 0`,
  padding: '0',
  textAlign: 'center' as const,
};

const projectImage = {
  borderRadius: TOKENS.radius.sm,
  margin: `${TOKENS.spacing.scale[5]} 0`,
};

const upcomingEvents = {
  margin: `${TOKENS.spacing.scale[7]} 0`,
};

const sectionTitle = {
  color: TOKENS.color.neutral.nearBlack,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.h3,
  fontWeight: TOKENS.typography.weights.bold,
  margin: `0 0 ${TOKENS.spacing.scale[5]} 0`,
  padding: '0',
  borderBottom: `2px solid ${TOKENS.color.brand.royalBlue}`,
  paddingBottom: `${TOKENS.spacing.scale[2]}`,
};

const eventCard = {
  backgroundColor: TOKENS.color.neutral.smoke,
  borderRadius: TOKENS.radius.sm,
  padding: `${TOKENS.spacing.scale[3]}`,
  margin: `${TOKENS.spacing.scale[3]} 0`,
};

const eventTitle = {
  color: TOKENS.color.neutral.nearBlack,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.body,
  fontWeight: TOKENS.typography.weights.bold,
  margin: `0 0 ${TOKENS.spacing.scale[1]} 0`,
  padding: '0',
};

const eventDate = {
  color: TOKENS.color.brand.royalBlue,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.small,
  fontWeight: TOKENS.typography.weights.bold,
  margin: `0 0 ${TOKENS.spacing.scale[2]} 0`,
  padding: '0',
};

const eventDescription = {
  color: TOKENS.color.neutral.charcoal,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.small,
  lineHeight: '20px',
  margin: '0',
  padding: '0',
};

const membershipSection = {
  margin: `${TOKENS.spacing.scale[7]} 0`,
};

// All footer and CTA styles are now handled by reusable components