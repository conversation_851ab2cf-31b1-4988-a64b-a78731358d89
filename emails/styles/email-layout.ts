import { TOKENS } from '../../lib/tokens';

/**
 * Email Layout Styles Module
 *
 * Contains layout-related styles for sections, containers, spacing, and structural elements.
 * Optimized for email client compatibility with table-free responsive layouts.
 *
 * @module email-layout
 */

export interface EmailLayoutStyles {
  // Sections
  headerSection: React.CSSProperties;
  headerGradient: React.CSSProperties;
  heroImageSection: React.CSSProperties;
  heroTitleSection: React.CSSProperties;
  heroDescriptionSection: React.CSSProperties;
  section: React.CSSProperties;
  sectionAlt: React.CSSProperties;
  footerSection: React.CSSProperties;

  // Containers
  logoContainer: React.CSSProperties;
  contentContainer: React.CSSProperties;
  flexContainer: React.CSSProperties;
  eventDetailsContainer: React.CSSProperties;
  eventDetailsHorizontal: React.CSSProperties;
  eventDetailItem: React.CSSProperties;
  eventDetailDivider: React.CSSProperties;
  eventDetailIcon: React.CSSProperties;
  buttonContainer: React.CSSProperties;

  // Spacing utilities
  sectionSpacing: React.CSSProperties;
  contentSpacing: React.CSSProperties;

  // Images and media
  logo: React.CSSProperties;
  heroLogo: React.CSSProperties;
  heroImage: React.CSSProperties;

  // Layout utilities
  fullWidth: React.CSSProperties;
  centered: React.CSSProperties;
  tableCell: React.CSSProperties;

  // Mobile-specific
  mobileSection: React.CSSProperties;
  mobileContainer: React.CSSProperties;
}

/**
 * Layout styles collection for email templates
 */
export const layoutStyles: EmailLayoutStyles = {
  /**
   * Header section - Fixed height centered branding (Figma spec: 66px)
   */
  headerSection: {
    backgroundColor: TOKENS.color.brand.royalBlue,
    height: '66px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center' as const,
    position: 'relative',
    /* Email client fallbacks */
    verticalAlign: 'middle',
  } as React.CSSProperties,

  /**
   * Header gradient transition section (Figma spec: 25px)
   */
  headerGradient: {
    background: 'linear-gradient(to bottom, #17458F, #0f3472)',
    height: '25px',
    width: '100%',
    /* Fallback for email clients */
    backgroundColor: TOKENS.color.brand.royalBlueDark,
  } as React.CSSProperties,

  /**
   * Hero image section - White background with centered image
   */
  heroImageSection: {
    backgroundColor: TOKENS.color.neutral.white,
    padding: '16px 48px',
    textAlign: 'center' as const,
    boxSizing: 'border-box' as const,
  } as React.CSSProperties,

  /**
   * Hero title section - Light blue tint background (Figma spec: 15% opacity)
   */
  heroTitleSection: {
    backgroundColor: 'rgba(23, 69, 143, 0.15)',
    padding: '24px 48px',
    textAlign: 'center' as const,
    boxSizing: 'border-box' as const,
    /* Fallback for email clients that don't support rgba */
    background: '#E8EDF7',
  } as React.CSSProperties,

  /**
   * Hero description section - White background with constrained width
   */
  heroDescriptionSection: {
    backgroundColor: TOKENS.color.neutral.white,
    padding: '24px 48px',
    textAlign: 'center' as const,
    boxSizing: 'border-box' as const,
  } as React.CSSProperties,

  /**
   * Standard content section
   */
  section: {
    padding: `${TOKENS.spacing.scale[6]} ${TOKENS.spacing.scale[4]}`,
    margin: `${TOKENS.spacing.scale[4]} 0`,
    backgroundColor: TOKENS.color.neutral.white,
    /* Standard section spacing and layout */
    textAlign: 'left' as const,
    boxSizing: 'border-box' as const,
  } as React.CSSProperties,

  /**
   * Alternative section - alternating backgrounds for visual hierarchy
   */
  sectionAlt: {
    padding: `${TOKENS.spacing.scale[6]} ${TOKENS.spacing.scale[4]}`,
    margin: `${TOKENS.spacing.scale[4]} 0`,
    backgroundColor: TOKENS.color.neutral.smoke,
    borderTop: `2px solid ${TOKENS.color.brand.gold}`,
    borderBottom: `2px solid ${TOKENS.color.brand.gold}`,
    /* Alternative section styling */
    textAlign: 'center' as const,
    boxSizing: 'border-box' as const,
  } as React.CSSProperties,

  /**
   * Footer section - closing content and branding
   */
  footerSection: {
    padding: `${TOKENS.spacing.scale[5]} ${TOKENS.spacing.scale[4]} 0`,
    backgroundColor: TOKENS.color.brand.royalBlue,
    color: TOKENS.color.neutral.white,
    /* Footer branding and contact info */
    textAlign: 'center' as const,
    position: 'relative',
    /* Ensure proper contrast for footer content */
    borderTop: `4px solid ${TOKENS.color.brand.gold}`,
  } as React.CSSProperties,

  /**
   * Logo container - wrapper for branding elements
   */
  logoContainer: {
    margin: `${TOKENS.spacing.scale[3]} auto ${TOKENS.spacing.scale[4]}`,
    textAlign: 'center' as const,
    /* Logo container positioning */
    display: 'block',
    maxWidth: '240px',
  } as React.CSSProperties,

  /**
   * Content container - generic content wrapper
   */
  contentContainer: {
    maxWidth: '600px',
    margin: '0 auto',
    padding: `0 ${TOKENS.spacing.scale[4]}`,
    /* Content container for responsive design */
    boxSizing: 'border-box' as const,
    textAlign: 'left' as const,
  } as React.CSSProperties,

  /**
   * Flex container - for horizontal alignments (email-safe)
   */
  flexContainer: {
    display: 'table',
    width: '100%',
    tableLayout: 'fixed' as const,
    /* Email-safe horizontal alignment */
    textAlign: 'center' as const,
  } as React.CSSProperties,

  /**
   * Event details container - structured information display
   */
  eventDetailsContainer: {
    backgroundColor: TOKENS.color.neutral.white,
    border: `2px solid ${TOKENS.color.neutral.smoke}`,
    borderRadius: TOKENS.radius.md,
    padding: TOKENS.spacing.scale[5],
    margin: `${TOKENS.spacing.scale[4]} 0`,
    /* Event details card styling */
    boxSizing: 'border-box' as const,
    position: 'relative',
  } as React.CSSProperties,

  /**
   * Event details horizontal layout (Figma spec: horizontal with icons)
   */
  eventDetailsHorizontal: {
    backgroundColor: '#fcfcfc',
    padding: '24px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    /* Email client fallback */
    textAlign: 'center' as const,
    boxSizing: 'border-box' as const,
  } as React.CSSProperties,

  /**
   * Individual event detail item (date/time with icon)
   */
  eventDetailItem: {
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    /* Email client fallback */
    textAlign: 'left' as const,
  } as React.CSSProperties,

  /**
   * Vertical divider between date and time sections
   */
  eventDetailDivider: {
    height: '34px',
    width: '1px',
    backgroundColor: '#d1d5db',
    margin: '0 32px',
    /* Email client fallback */
    borderLeft: '1px solid #d1d5db',
  } as React.CSSProperties,

  /**
   * Icon container for calendar/clock icons
   */
  eventDetailIcon: {
    width: '46px',
    height: '43px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f3f4f6',
    borderRadius: '4px',
    /* Email client fallback */
    textAlign: 'center' as const,
    verticalAlign: 'middle',
  } as React.CSSProperties,

  /**
   * Button container - action button grouping
   */
  buttonContainer: {
    textAlign: 'center' as const,
    margin: `${TOKENS.spacing.scale[5]} 0`,
    /* Button container for CTAs */
    padding: `0 ${TOKENS.spacing.scale[3]}`,
  } as React.CSSProperties,

  /**
   * Section spacing utility
   */
  sectionSpacing: {
    margin: `${TOKENS.spacing.scale[6]} 0`,
    /* Consistent section spacing */
    padding: '0',
  } as React.CSSProperties,

  /**
   * Content spacing utility
   */
  contentSpacing: {
    margin: `${TOKENS.spacing.scale[4]} 0`,
    /* Content element spacing */
    padding: '0',
  } as React.CSSProperties,

  /**
   * Logo image styling
   */
  logo: {
    maxWidth: '200px',
    height: 'auto',
    margin: `${TOKENS.spacing.scale[2]} auto`,
    /* Logo image properties */
    display: 'block',
    textAlign: 'center' as const,
    /* Prevent image scaling issues */
    objectFit: 'contain' as const,
  } as React.CSSProperties,

  /**
   * Hero section logo variant
   */
  heroLogo: {
    maxWidth: '180px',
    height: 'auto',
    margin: `${TOKENS.spacing.scale[3]} auto ${TOKENS.spacing.scale[2]}`,
    /* Hero logo variant */
    display: 'block',
    textAlign: 'center' as const,
    objectFit: 'contain' as const,
    /* Ensure visibility on blue background */
    filter: 'brightness(1.1)',
  } as React.CSSProperties,

  /**
   * Hero background image styling
   */
  heroImage: {
    maxWidth: '100%',
    height: 'auto',
    margin: `${TOKENS.spacing.scale[4]} auto 0`,
    /* Hero image styling */
    display: 'block',
    textAlign: 'center' as const,
    objectFit: 'cover' as const,
    borderRadius: TOKENS.radius.md,
    /* Prevent layout shifts */
    aspectRatio: '16/9',
  } as React.CSSProperties,

  /**
   * Full width utility
   */
  fullWidth: {
    width: '100%',
    boxSizing: 'border-box' as const,
    /* Full width element styling */
    maxWidth: '100%',
  } as React.CSSProperties,

  /**
   * Centered utility
   */
  centered: {
    textAlign: 'center' as const,
    margin: '0 auto',
    /* Centering utility */
    display: 'block',
  } as React.CSSProperties,

  /**
   * Table cell utility for email table fallbacks
   */
  tableCell: {
    display: 'table-cell',
    verticalAlign: 'top',
    padding: `0 ${TOKENS.spacing.scale[2]}`,
    /* Table cell styling for email layouts */
    textAlign: 'left' as const,
  } as React.CSSProperties,

  /**
   * Mobile section variant
   */
  mobileSection: {
    padding: `${TOKENS.spacing.scale[4]} ${TOKENS.spacing.scale[3]}`,
    margin: `${TOKENS.spacing.scale[3]} 0`,
    /* Mobile-optimized section */
    boxSizing: 'border-box' as const,
    textAlign: 'left' as const,
  } as React.CSSProperties,

  /**
   * Mobile container variant
   */
  mobileContainer: {
    margin: '0 auto',
    padding: `0 ${TOKENS.spacing.scale[2]}`,
    width: '100%',
    maxWidth: '100%',
    /* Mobile container with reduced margins */
    boxSizing: 'border-box' as const,
  } as React.CSSProperties,
};

/**
 * Detail row layout for event information
 * Note: This creates a table-free responsive detail layout
 */
export const detailRow: React.CSSProperties = {
  display: 'table',
  width: '100%',
  margin: `${TOKENS.spacing.scale[3]} 0`,
  tableLayout: 'fixed' as const,
  /* Detail row for key-value pairs */
  borderSpacing: '0',
  borderCollapse: 'separate' as const,
};

/**
 * Logo text styling (for text-based logos or mottos)
 */
export const logoText: React.CSSProperties = {
  color: TOKENS.color.brand.royalBlue,
  fontFamily: TOKENS.typography.family.primary,
  fontSize: TOKENS.typography.scale.h3,
  fontWeight: TOKENS.typography.weights.bold,
  margin: `${TOKENS.spacing.scale[2]} 0`,
  textAlign: 'center' as const,
  /* Logo text styling */
  textTransform: 'uppercase' as const,
  letterSpacing: '0.5px',
  lineHeight: '1.2',
};

/**
 * Utility function to create responsive section styles
 * @param isMobile - Whether to use mobile styling
 * @returns Responsive section styles
 */
export const getResponsiveSection = (isMobile: boolean = false): React.CSSProperties => {
  const baseStyle = {
    margin: isMobile ? `${TOKENS.spacing.scale[3]} 0` : `${TOKENS.spacing.scale[6]} 0`,
    padding: isMobile
      ? `${TOKENS.spacing.scale[4]} ${TOKENS.spacing.scale[3]}`
      : `${TOKENS.spacing.scale[6]} ${TOKENS.spacing.scale[4]}`,
    boxSizing: 'border-box' as const,
  };

  return baseStyle;
};

/**
 * Gradient styles for visual enhancements (email-safe)
 */
export const gradientStyles = {
  primary: `linear-gradient(135deg, ${TOKENS.color.brand.royalBlue} 0%, ${TOKENS.color.brand.royalBlueDark} 100%)`,
  secondary: `linear-gradient(90deg, ${TOKENS.color.brand.gold} 0%, ${TOKENS.color.neutral.white} 100%)`,
};