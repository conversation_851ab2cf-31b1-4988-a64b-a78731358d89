import { TOKENS } from '../../lib/tokens';

/**
 * Email Typography Styles Module
 *
 * Contains typography-related styles for headings, text, fonts, lists, and tables.
 * Optimized for email client compatibility with web-safe fonts and proper fallbacks.
 *
 * @module email-typography
 */

export interface EmailTypographyStyles {
  // Headings
  h1: React.CSSProperties;
  h2: React.CSSProperties;
  h3: React.CSSProperties;
  h4: React.CSSProperties;
  sectionHeading: React.CSSProperties;
  headerTitle: React.CSSProperties;

  // Text variants
  text: React.CSSProperties;
  smallText: React.CSSProperties;
  welcomeText: React.CSSProperties;
  bodyText: React.CSSProperties;
  disclaimerText: React.CSSProperties;
  textHighlight: React.CSSProperties;

  // Font utilities
  fontFamily: string;
  arabicFontFamily: string;

  // Table typography
  detailLabel: React.CSSProperties;
  detailValue: React.CSSProperties;

  // List styles (email-safe)
  listItem: React.CSSProperties;
  bulletPoint: React.CSSProperties;

  // Link styles
  link: React.CSSProperties;
  linkHover: React.CSSProperties;
}

/**
 * Font family resolver with culture-aware defaults
 * @param language - Language code for font selection
 * @returns CSS-compatible font-family string
 */
export const getFontFamily = (language: string): string => {
  switch (language) {
    case 'ar':
      return '"Tajawal", Arial, Helvetica, sans-serif';
    case 'fr':
    case 'en':
    default:
      return '"Open Sans", Arial, Helvetica, sans-serif';
  }
};

/**
 * Typography styles collection for email templates
 */
export const typographyStyles: EmailTypographyStyles = {
  /**
   * H1 heading - main page titles
   */
  h1: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.h1,
    fontWeight: TOKENS.typography.weights.bold,
    margin: `${TOKENS.spacing.scale[10]} 0`,
    padding: '0',
    textAlign: 'center' as const,
    lineHeight: '1.2',
    /* Email-safe text rendering */
    textRendering: 'optimizeLegibility',
    fontStyle: 'normal',
  } as React.CSSProperties,

  /**
   * H2 heading - section headings
   */
  h2: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.h2,
    fontWeight: TOKENS.typography.weights.bold,
    margin: `${TOKENS.spacing.scale[7]} 0`,
    padding: '0',
    textAlign: 'center' as const,
    lineHeight: '1.3',
    textRendering: 'optimizeLegibility',
  } as React.CSSProperties,

  /**
   * H3 heading - subsection headings
   */
  h3: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.h3,
    fontWeight: TOKENS.typography.weights.bold,
    margin: `${TOKENS.spacing.scale[5]} 0`,
    padding: '0',
    textAlign: 'left' as const,
    lineHeight: '1.4',
    textRendering: 'optimizeLegibility',
  } as React.CSSProperties,

  /**
   * H4 heading - small section headings
   */
  h4: {
    color: TOKENS.color.neutral.charcoal,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    fontWeight: TOKENS.typography.weights.semibold,
    margin: `${TOKENS.spacing.scale[4]} 0`,
    padding: '0',
    textAlign: 'left' as const,
    lineHeight: '1.4',
    textRendering: 'optimizeLegibility',
  } as React.CSSProperties,

  /**
   * Section heading - standard for Rotary email sections
   */
  sectionHeading: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.h3,
    fontWeight: TOKENS.typography.weights.bold,
    margin: `${TOKENS.spacing.scale[5]} 0`,
    padding: '0',
    textAlign: 'center' as const,
    lineHeight: '1.3',
    borderBottom: `2px solid ${TOKENS.color.brand.royalBlue}`,
    paddingBottom: TOKENS.spacing.scale[2],
    textRendering: 'optimizeLegibility',
  } as React.CSSProperties,

  /**
   * Header title - for email header branding
   */
  headerTitle: {
    color: TOKENS.color.brand.royalBlue,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.h2,
    fontWeight: TOKENS.typography.weights.bold,
    margin: `${TOKENS.spacing.scale[2]} 0`,
    padding: '0',
    textAlign: 'center' as const,
    lineHeight: '1.2',
    textRendering: 'optimizeLegibility',
    /* Rotary branding specific */
    textTransform: 'uppercase' as const,
    letterSpacing: '0.5px',
  } as React.CSSProperties,

  /**
   * Standard body text
   */
  text: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    lineHeight: '26px',
    margin: `${TOKENS.spacing.scale[3]} 0`,
    padding: '0',
    textAlign: 'left' as const,
    /* Email-safe text properties */
    wordWrap: 'break-word' as const,
    overflowWrap: 'break-word' as const,
  } as React.CSSProperties,

  /**
   * Welcome text - greeting text
   */
  welcomeText: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.h3,
    fontWeight: TOKENS.typography.weights.medium,
    lineHeight: '28px',
    margin: `${TOKENS.spacing.scale[4]} 0`,
    padding: '0',
    textAlign: 'center' as const,
    wordWrap: 'break-word' as const,
    overflowWrap: 'break-word' as const,
    /* Welcome text emphasis */
    fontStyle: 'normal',
  } as React.CSSProperties,

  /**
   * Body text - general content text
   */
  bodyText: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    lineHeight: '26px',
    margin: `${TOKENS.spacing.scale[3]} 0`,
    padding: '0',
    textAlign: 'left' as const,
    /* Email-safe text properties */
    wordWrap: 'break-word' as const,
    overflowWrap: 'break-word' as const,
  } as React.CSSProperties,

  /**
   * Small text - captions and legal text
   */
  smallText: {
    color: TOKENS.color.neutral.charcoal,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.small,
    lineHeight: '20px',
    margin: `${TOKENS.spacing.scale[2]} 0`,
    padding: '0',
    textAlign: 'left' as const,
    /* Fine print styling */
    opacity: '0.8',
  } as React.CSSProperties,

  /**
   * Disclaimer text - legal and policy text
   */
  disclaimerText: {
    color: TOKENS.color.neutral.charcoal,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.small,
    lineHeight: '18px',
    margin: `${TOKENS.spacing.scale[3]} 0`,
    padding: '0',
    textAlign: 'center' as const,
    fontStyle: 'italic' as const,
    /* Disclaimer styling */
    borderTop: `1px solid ${TOKENS.color.neutral.smoke}`,
    paddingTop: TOKENS.spacing.scale[3],
  } as React.CSSProperties,

  /**
   * Highlighted text - emphasis for important information
   */
  textHighlight: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    lineHeight: '26px',
    margin: `${TOKENS.spacing.scale[3]} 0`,
    padding: `2px ${TOKENS.spacing.scale[1]}`,
    textAlign: 'left' as const,
    fontWeight: TOKENS.typography.weights.semibold,
    backgroundColor: TOKENS.color.neutral.smoke,
    borderRadius: TOKENS.radius.sm,
    display: 'inline',
    /* Highlight box properties for better visibility */
    boxShadow: '0 1px 2px rgba(0,0,0,0.1)',
    /* Email-safe text properties */
    wordWrap: 'break-word' as const,
    overflowWrap: 'break-word' as const,
  } as React.CSSProperties,

  /**
   * Detail label - for form labels and field descriptions
   */
  detailLabel: {
    color: TOKENS.color.neutral.charcoal,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    fontWeight: TOKENS.typography.weights.medium,
    margin: '0',
    padding: '0',
    /* Label styling */
    textTransform: 'capitalize' as const,
  } as React.CSSProperties,

  /**
   * Detail value - for form values and data display
   */
  detailValue: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    fontWeight: TOKENS.typography.weights.semibold,
    margin: '0',
    padding: '0',
    /* Value styling */
    wordBreak: 'break-word' as const,
  } as React.CSSProperties,

  /**
   * List item - email-safe list styling
   */
  listItem: {
    color: TOKENS.color.neutral.nearBlack,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    lineHeight: '24px',
    margin: `${TOKENS.spacing.scale[1]} 0`,
    padding: '0 0 0 20px',
    textAlign: 'left' as const,
    /* Email-safe list styling */
    position: 'relative' as const,
    listStyle: 'none',
  } as React.CSSProperties,

  /**
   * Bullet point styling for custom bullets
   */
  bulletPoint: {
    position: 'absolute' as const,
    left: '0',
    top: '0',
    color: TOKENS.color.brand.royalBlue,
    fontWeight: TOKENS.typography.weights.bold,
  } as React.CSSProperties,

  /**
   * Link text styling
   */
  link: {
    color: TOKENS.color.brand.royalBlue,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    fontWeight: TOKENS.typography.weights.medium,
    textDecoration: 'underline',
    border: 'none',
    /* Email-safe link styling */
    textUnderlineOffset: '2px',
    textDecorationColor: TOKENS.color.brand.royalBlue,
  } as React.CSSProperties,

  /**
   * Link hover state (limited support in email clients)
   */
  linkHover: {
    color: TOKENS.color.brand.royalBlue,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    fontWeight: TOKENS.typography.weights.medium,
    textDecoration: 'none',
    border: 'none',
    /* Fallback for email clients without hover support */
    borderBottom: `1px solid ${TOKENS.color.brand.royalBlue}`,
    /* Email-safe link styling */
    textUnderlineOffset: '2px',
    textDecorationColor: TOKENS.color.brand.royalBlue,
  } as React.CSSProperties,

  /**
   * Primary font family for multilingual support
   */
  fontFamily: TOKENS.typography.family.primary,

  /**
   * Arabic font family for RTL languages
   */
  arabicFontFamily: '"Tajawal", Arial, Helvetica, sans-serif',

};

/**
 * Utility function to get responsive typography
 * @param baseSize - Base font size
 * @param isMobile - Whether to use mobile sizing
 * @returns Responsive font size string
 */
export const getResponsiveFontSize = (baseSize: string, isMobile: boolean = false): string => {
  // Simple responsive sizing - can be expanded based on requirements
  const sizeMap: Record<string, { mobile: string; desktop: string }> = {
    '32px': { mobile: '24px', desktop: '32px' },
    '26px': { mobile: '20px', desktop: '26px' },
    '20px': { mobile: '16px', desktop: '20px' },
    '16px': { mobile: '14px', desktop: '16px' },
    '14px': { mobile: '12px', desktop: '14px' },
  };

  const responsive = sizeMap[baseSize];
  return responsive ? (isMobile ? responsive.mobile : responsive.desktop) : baseSize;
};