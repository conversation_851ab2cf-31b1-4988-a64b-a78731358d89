import { TOKENS } from '../../lib/tokens';

/**
 * Email Themes Styles Module
 *
 * Contains theme-related styles, colors, branding, and visual variants.
 * Supports multiple event types with consistent branding.
 *
 * @module email-themes
 */

export interface EmailTheme {
  primaryColor: string;
  secondaryColor: string;
  backgroundColor: string;
  textColor: string;
  accentColor?: string;
  borderColor: string;
}

export interface EmailThemeStyles {
  // Rotary default theme
  default: EmailTheme;

  // Event type themes
  webinar: EmailTheme;
  club: EmailTheme;
  fundraiser: EmailTheme;

  // Branding variants
  professional: EmailTheme;
  casual: EmailTheme;

  // Utility themes
  highContrast: EmailTheme;

  // Component theme helpers
  buttonThemes: {
    primary: React.CSSProperties;
    secondary: React.CSSProperties;
    outline: React.CSSProperties;
  };

  // Color utilities
  overlay: React.CSSProperties;
  gradientBackground: string;
  focusRing: React.CSSProperties;
}

/**
 * Default Rotary theme configuration
 */
const defaultTheme: EmailTheme = {
  primaryColor: TOKENS.color.brand.royalBlue,
  secondaryColor: TOKENS.color.brand.gold,
  backgroundColor: TOKENS.color.neutral.white,
  textColor: TOKENS.color.neutral.nearBlack,
  borderColor: TOKENS.color.neutral.smoke,
};

/**
 * Event-specific themes for different Rotary activities
 */
const eventThemes = {
  webinar: {
    ...defaultTheme,
    accentColor: TOKENS.color.brand.gold,
    backgroundColor: TOKENS.color.neutral.smoke,
  } as EmailTheme,

  club: {
    ...defaultTheme,
    secondaryColor: TOKENS.color.brand.gold,
    accentColor: TOKENS.color.status.success,
  } as EmailTheme,

  fundraiser: {
    ...defaultTheme,
    primaryColor: TOKENS.color.status.success,
    accentColor: TOKENS.color.brand.gold,
  } as EmailTheme,
};

/**
 * Alternative branding themes
 */
const brandingThemes = {
  professional: {
    ...defaultTheme,
    primaryColor: TOKENS.color.brand.royalBlueDark,
    textColor: TOKENS.color.neutral.trueBlack,
  } as EmailTheme,

  casual: {
    ...defaultTheme,
    secondaryColor: TOKENS.color.brand.gold,
    backgroundColor: TOKENS.color.neutral.smoke,
  } as EmailTheme,
};

/**
 * Accessibility theme for high contrast
 */
const accessibilityTheme: EmailTheme = {
  primaryColor: TOKENS.color.neutral.trueBlack,
  secondaryColor: TOKENS.color.neutral.trueBlack,
  backgroundColor: TOKENS.color.neutral.white,
  textColor: TOKENS.color.neutral.trueBlack,
  borderColor: TOKENS.color.neutral.trueBlack,
};

/**
 * Theme styles collection with complete theming system
 */
export const themeStyles: EmailThemeStyles = {
  /**
   * Default Rotary theme
   */
  default: defaultTheme,

  /**
   * Event type specific themes
   */
  webinar: eventThemes.webinar,
  club: eventThemes.club,
  fundraiser: eventThemes.fundraiser,

  /**
   * Alternative branding themes
   */
  professional: brandingThemes.professional,
  casual: brandingThemes.casual,

  /**
   * High contrast accessibility theme
   */
  highContrast: accessibilityTheme,

  /**
   * Themed button styles
   */
  buttonThemes: {
    primary: {
      backgroundColor: '$primaryColor',
      borderColor: '$primaryColor',
      color: TOKENS.color.neutral.white,
    } as React.CSSProperties,

    secondary: {
      backgroundColor: '$secondaryColor',
      borderColor: '$secondaryColor',
      color: TOKENS.color.neutral.nearBlack,
    } as React.CSSProperties,

    outline: {
      backgroundColor: 'transparent',
      borderColor: '$primaryColor',
      color: '$primaryColor',
    } as React.CSSProperties,
  },

  /**
   * Background overlay utility
   */
  overlay: {
    backgroundColor: TOKENS.color.surface.overlay,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    /* Overlay for contrast enhancement */
    zIndex: 1,
  } as React.CSSProperties,

  /**
   * Gradient background for hero sections
   */
  gradientBackground: `linear-gradient(135deg, ${defaultTheme.primaryColor} 0%, ${defaultTheme.secondaryColor} 100%)`,

  /**
   * Focus ring for accessibility
   */
  focusRing: {
    outline: `3px solid ${TOKENS.color.status.info}`,
    outlineOffset: '2px',
    borderRadius: TOKENS.radius.sm,
  } as React.CSSProperties,
};

/**
 * Theme getter utility
 * @param themeName - Name of the theme to get
 * @param eventType - Optional event type override
 * @returns Theme configuration
 */
export const getTheme = (themeName: keyof EmailThemeStyles = 'default', eventType?: string): EmailTheme => {
  if (themeName === 'default' && eventType && eventType in eventThemes) {
    const eventTheme = eventThemes[eventType as keyof typeof eventThemes];
    return eventTheme || defaultTheme;
  }

  if (themeName in themeStyles) {
    const theme = themeStyles[themeName];
    if (typeof theme === 'object' && 'primaryColor' in theme) {
      return theme as EmailTheme;
    }
  }

  return defaultTheme;
};

/**
 * Apply theme colors to component styles
 * @param componentStyle - Base component style
 * @param theme - Theme to apply
 * @returns Themed component style
 */
export const applyTheme = (componentStyle: React.CSSProperties, theme: EmailTheme): React.CSSProperties => {
  const themedStyle = { ...componentStyle };

  // Replace theme variable placeholders with actual values
  Object.entries(themedStyle).forEach(([key, value]) => {
    if (typeof value === 'string') {
      switch (value) {
        case '$primaryColor':
          (themedStyle as any)[key] = theme.primaryColor;
          break;
        case '$secondaryColor':
          (themedStyle as any)[key] = theme.secondaryColor;
          break;
        case '$backgroundColor':
          (themedStyle as any)[key] = theme.backgroundColor;
          break;
        case '$textColor':
          (themedStyle as any)[key] = theme.textColor;
          break;
        case '$borderColor':
          (themedStyle as any)[key] = theme.borderColor;
          break;
        case '$accentColor':
          (themedStyle as any)[key] = theme.accentColor || theme.secondaryColor;
          break;
      }
    }
  });

  return themedStyle;
};

/**
 * Generate themed email styles
 * @param customTheme - Custom theme override
 * @returns Complete themed styles object
 */
export const generateThemedStyles = (customTheme?: Partial<EmailTheme>) => {
  const currentTheme: EmailTheme = {
    ...defaultTheme,
    ...customTheme,
  };

  const themedButtonStyles = {
    primary: {
      backgroundColor: currentTheme.primaryColor,
      border: `2px solid ${currentTheme.primaryColor}`,
      color: TOKENS.color.neutral.white,
      borderRadius: TOKENS.radius.sm,
      padding: '14px 28px',
      fontSize: '16px',
      fontWeight: TOKENS.typography.weights.bold,
      fontFamily: TOKENS.typography.family.primary,
      textAlign: 'center' as const,
      textDecoration: 'none',
      display: 'inline-block',
      minWidth: '180px',
      margin: '8px',
      transition: 'all 0.2s ease',
    } as React.CSSProperties,

    secondary: {
      backgroundColor: currentTheme.secondaryColor,
      border: `2px solid ${currentTheme.secondaryColor}`,
      color: TOKENS.color.neutral.nearBlack,
      borderRadius: TOKENS.radius.sm,
      padding: '12px 24px',
      fontSize: '16px',
      fontWeight: TOKENS.typography.weights.semibold,
      fontFamily: TOKENS.typography.family.primary,
      textAlign: 'center' as const,
      textDecoration: 'none',
      display: 'inline-block',
      minWidth: '160px',
      margin: '8px',
      transition: 'all 0.2s ease',
    } as React.CSSProperties,

    outline: {
      backgroundColor: 'transparent',
      border: `2px solid ${currentTheme.primaryColor}`,
      color: currentTheme.primaryColor,
      borderRadius: TOKENS.radius.sm,
      padding: '12px 24px',
      fontSize: '16px',
      fontWeight: TOKENS.typography.weights.medium,
      fontFamily: TOKENS.typography.family.primary,
      textAlign: 'center' as const,
      textDecoration: 'none',
      display: 'inline-block',
      minWidth: '160px',
      margin: '8px',
      transition: 'all 0.2s ease',
    } as React.CSSProperties,
  };

  return {
    theme: currentTheme,
    buttonStyles: themedButtonStyles,
  };
};

/**
 * Event type specific color schemes
 */
export const eventColorSchemes = {
  webinar: {
    header: TOKENS.color.brand.royalBlue,
    accent: TOKENS.color.brand.gold,
    content: TOKENS.color.neutral.smoke,
  },

  club: {
    header: TOKENS.color.brand.gold,
    accent: TOKENS.color.status.success,
    content: TOKENS.color.neutral.white,
  },

  fundraiser: {
    header: TOKENS.color.status.success,
    accent: TOKENS.color.brand.royalBlue,
    content: TOKENS.color.neutral.smoke,
  },
} as const;

/**
 * Theme-aware utility functions
 */
export const themeUtils = {
  /**
   * Get contrast color for background
   */
  getContrastColor: (backgroundColor: string): string => {
    // Simplified contrast calculation
    const color = backgroundColor.replace('#', '');
    const r = parseInt(color.substr(0, 2), 16);
    const g = parseInt(color.substr(2, 2), 16);
    const b = parseInt(color.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;

    return brightness > 128 ? TOKENS.color.neutral.nearBlack : TOKENS.color.neutral.white;
  },

  /**
   * Generate opaque overlay for text on images
   */
  getOverlayStyles: (opacity: number = 0.6): React.CSSProperties => ({
    backgroundColor: `rgba(0, 0, 0, ${opacity})`,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 1,
  }),

  /**
   * Email-safe box shadow with theme colors
   */
  getThemeShadow: (theme: EmailTheme, intensity: 'low' | 'medium' | 'high' = 'medium'): string => {
    const colors = {
      low: 'rgba(0,0,0,0.05)',
      medium: 'rgba(0,0,0,0.1)',
      high: 'rgba(0,0,0,0.2)',
    };

    return `0 2px 8px ${colors[intensity]}`;
  },
};