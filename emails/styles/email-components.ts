import { TOKENS } from '../../lib/tokens';

/**
 * Email Components Styles Module
 *
 * Contains component-specific styles for buttons, forms, tables, cards, and UI elements.
 * Optimized for email client compatibility with clear visual hierarchy.
 *
 * @module email-components
 */

export interface EmailComponentStyles {
  // Buttons
  buttonPrimary: React.CSSProperties;
  buttonSecondary: React.CSSProperties;
  buttonOutline: React.CSSProperties;

  // Cards and containers
  card: React.CSSProperties;
  eventCard: React.CSSProperties;
  heroImage: React.CSSProperties;

  // Tables
  agendaTable: React.CSSProperties;
  agendaRow: React.CSSProperties;
  agendaCellTime: React.CSSProperties;
  agendaCellDescription: React.CSSProperties;

  // Form elements
  inputField: React.CSSProperties;
  formGroup: React.CSSProperties;

  // Interactive elements
  linkButton: React.CSSProperties;
  ctaContainer: React.CSSProperties;

  // Status indicators
  successIndicator: React.CSSProperties;
  warningIndicator: React.CSSProperties;
  errorIndicator: React.CSSProperties;

  // Special components
  speakerBlock: React.CSSProperties;
  benefitBlock: React.CSSProperties;
  detailBlock: React.CSSProperties;
}

/**
 * Component styles collection for email templates
 */
export const componentStyles: EmailComponentStyles = {
  /**
   * Primary button styling - main call-to-actions (Figma spec: no border, 6px radius, 12px 32px padding)
   */
  buttonPrimary: {
    backgroundColor: TOKENS.color.brand.royalBlue,
    border: 'none',
    color: TOKENS.color.neutral.white,
    borderRadius: '6px',
    padding: '12px 32px',
    fontSize: '16px',
    fontWeight: TOKENS.typography.weights.semibold,
    fontFamily: TOKENS.typography.family.primary,
    textAlign: 'center' as const,
    textDecoration: 'none',
    display: 'inline-block',
    /* Email-safe button properties */
    msTextSizeAdjust: '100%',
    webkitTextSizeAdjust: '100%',
    minWidth: '180px',
    margin: '8px',
    /* Button interaction states (limited email support) */
    transition: 'background-color 0.2s ease',
  } as React.CSSProperties,

  /**
   * Secondary button styling - alternative actions
   */
  buttonSecondary: {
    backgroundColor: TOKENS.color.brand.gold,
    border: `2px solid ${TOKENS.color.brand.gold}`,
    color: TOKENS.color.neutral.nearBlack,
    borderRadius: TOKENS.radius.sm,
    padding: '12px 24px',
    fontSize: '16px',
    fontWeight: TOKENS.typography.weights.semibold,
    fontFamily: TOKENS.typography.family.primary,
    textAlign: 'center' as const,
    textDecoration: 'none',
    display: 'inline-block',
    /* Secondary button properties */
    msTextSizeAdjust: '100%',
    webkitTextSizeAdjust: '100%',
    minWidth: '160px',
    margin: '8px',
    transition: 'background-color 0.2s ease',
  } as React.CSSProperties,

  /**
   * Outline button styling - tertiary actions
   */
  buttonOutline: {
    backgroundColor: 'transparent',
    border: `2px solid ${TOKENS.color.brand.royalBlue}`,
    color: TOKENS.color.brand.royalBlue,
    borderRadius: TOKENS.radius.sm,
    padding: '12px 24px',
    fontSize: '16px',
    fontWeight: TOKENS.typography.weights.medium,
    fontFamily: TOKENS.typography.family.primary,
    textAlign: 'center' as const,
    textDecoration: 'none',
    display: 'inline-block',
    /* Outline button properties */
    msTextSizeAdjust: '100%',
    webkitTextSizeAdjust: '100%',
    minWidth: '160px',
    margin: '8px',
    transition: 'all 0.2s ease',
  } as React.CSSProperties,

  /**
   * Generic card container
   */
  card: {
    backgroundColor: TOKENS.color.neutral.white,
    border: `1px solid ${TOKENS.color.neutral.smoke}`,
    borderRadius: TOKENS.radius.md,
    padding: TOKENS.spacing.scale[5],
    margin: `${TOKENS.spacing.scale[4]} 0`,
    /* Card shadow for depth */
    boxShadow: `0 2px 8px rgba(0,0,0,0.1)`,
    /* Email-safe card properties */
    maxWidth: '100%',
    overflow: 'hidden',
  } as React.CSSProperties,

  /**
   * Event-specific card variant
   */
  eventCard: {
    backgroundColor: TOKENS.color.neutral.white,
    border: `2px solid ${TOKENS.color.brand.gold}`,
    borderRadius: TOKENS.radius.md,
    padding: TOKENS.spacing.scale[6],
    margin: `${TOKENS.spacing.scale[5]} 0`,
    /* Event card with branding */
    boxShadow: `0 4px 12px rgba(0,0,0,0.15)`,
    position: 'relative',
    /* Event card specific styling */
    borderLeftWidth: '6px',
  } as React.CSSProperties,

  /**
   * Hero image styling (Figma spec: 504x322px with 8px border radius)
   */
  heroImage: {
    width: '504px',
    height: '322px',
    maxWidth: '100%',
    borderRadius: '8px',
    display: 'block',
    margin: '0 auto',
    /* Email client compatibility */
    border: 'none',
    outline: 'none',
  } as React.CSSProperties,

  /**
   * Agenda table container
   */
  agendaTable: {
    width: '100%',
    borderCollapse: 'collapse' as const,
    margin: `${TOKENS.spacing.scale[4]} 0`,
    /* Agenda table styling */
    tableLayout: 'auto',
    backgroundColor: TOKENS.color.neutral.white,
    border: `1px solid ${TOKENS.color.neutral.smoke}`,
    borderRadius: TOKENS.radius.sm,
    overflow: 'hidden',
  } as React.CSSProperties,

  /**
   * Individual agenda row
   */
  agendaRow: {
    borderBottom: `1px solid ${TOKENS.color.neutral.smoke}`,
    /* Alternate row colors for better readability */
    backgroundColor: 'transparent',
    transition: 'background-color 0.15s ease',
  } as React.CSSProperties,

  /**
   * Time cell in agenda table
   */
  agendaCellTime: {
    padding: `${TOKENS.spacing.scale[3]} ${TOKENS.spacing.scale[4]}`,
    fontWeight: TOKENS.typography.weights.semibold,
    color: TOKENS.color.brand.royalBlue,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    /* Time cell specific styling */
    minWidth: '120px',
    textAlign: 'left' as const,
    verticalAlign: 'top',
    borderRight: `1px solid ${TOKENS.color.neutral.smoke}`,
  } as React.CSSProperties,

  /**
   * Description cell in agenda table
   */
  agendaCellDescription: {
    padding: `${TOKENS.spacing.scale[3]} ${TOKENS.spacing.scale[4]}`,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    color: TOKENS.color.neutral.nearBlack,
    lineHeight: '1.6',
    /* Description cell styling */
    textAlign: 'left' as const,
    verticalAlign: 'top',
    wordWrap: 'break-word' as const,
    overflowWrap: 'break-word' as const,
  } as React.CSSProperties,

  /**
   * Input field styling (limited email client support)
   */
  inputField: {
    border: `1px solid ${TOKENS.color.neutral.smoke}`,
    borderRadius: TOKENS.radius.sm,
    padding: `${TOKENS.spacing.scale[3]} ${TOKENS.spacing.scale[4]}`,
    fontSize: TOKENS.typography.scale.body,
    fontFamily: TOKENS.typography.family.primary,
    /* Input field properties */
    width: '100%',
    maxWidth: '300px',
    boxSizing: 'border-box' as const,
    color: TOKENS.color.neutral.nearBlack,
    backgroundColor: TOKENS.color.neutral.white,
  } as React.CSSProperties,

  /**
   * Form group container
   */
  formGroup: {
    margin: `${TOKENS.spacing.scale[4]} 0`,
    textAlign: 'left' as const,
    /* Form group spacing */
    padding: '0',
  } as React.CSSProperties,

  /**
   * Link button styling
   */
  linkButton: {
    color: TOKENS.color.brand.royalBlue,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    fontWeight: TOKENS.typography.weights.medium,
    textDecoration: 'underline',
    /* Link button properties */
    display: 'inline-block',
    margin: `${TOKENS.spacing.scale[2]} ${TOKENS.spacing.scale[1]}`,
    padding: `${TOKENS.spacing.scale[2]} ${TOKENS.spacing.scale[3]}`,
    borderRadius: TOKENS.radius.sm,
    transition: 'all 0.2s ease',
  } as React.CSSProperties,

  /**
   * CTA container for multiple buttons
   */
  ctaContainer: {
    textAlign: 'center' as const,
    margin: `${TOKENS.spacing.scale[6]} 0`,
    /* CTA container layout */
    padding: `0 ${TOKENS.spacing.scale[2]}`,
  } as React.CSSProperties,

  /**
   * Success indicator styling
   */
  successIndicator: {
    backgroundColor: TOKENS.color.status.success,
    color: TOKENS.color.neutral.white,
    borderRadius: TOKENS.radius.sm,
    padding: `${TOKENS.spacing.scale[1]} ${TOKENS.spacing.scale[2]}`,
    /* Success indicator */
    fontSize: TOKENS.typography.scale.small,
    fontWeight: TOKENS.typography.weights.medium,
    display: 'inline-block',
    margin: `${TOKENS.spacing.scale[1]} 0`,
    textAlign: 'center' as const,
  } as React.CSSProperties,

  /**
   * Warning indicator styling
   */
  warningIndicator: {
    backgroundColor: TOKENS.color.status.warning,
    color: TOKENS.color.neutral.nearBlack,
    borderRadius: TOKENS.radius.sm,
    padding: `${TOKENS.spacing.scale[1]} ${TOKENS.spacing.scale[2]}`,
    /* Warning indicator */
    fontSize: TOKENS.typography.scale.small,
    fontWeight: TOKENS.typography.weights.medium,
    display: 'inline-block',
    margin: `${TOKENS.spacing.scale[1]} 0`,
    textAlign: 'center' as const,
  } as React.CSSProperties,

  /**
   * Error indicator styling
   */
  errorIndicator: {
    backgroundColor: TOKENS.color.status.error,
    color: TOKENS.color.neutral.white,
    borderRadius: TOKENS.radius.sm,
    padding: `${TOKENS.spacing.scale[1]} ${TOKENS.spacing.scale[2]}`,
    /* Error indicator */
    fontSize: TOKENS.typography.scale.small,
    fontWeight: TOKENS.typography.weights.medium,
    display: 'inline-block',
    margin: `${TOKENS.spacing.scale[1]} 0`,
    textAlign: 'center' as const,
  } as React.CSSProperties,

  /**
   * Speaker information block
   */
  speakerBlock: {
    backgroundColor: TOKENS.color.neutral.smoke,
    borderRadius: TOKENS.radius.md,
    padding: TOKENS.spacing.scale[5],
    margin: `${TOKENS.spacing.scale[4]} 0`,
    /* Speaker block layout */
    display: 'table',
    width: '100%',
    tableLayout: 'fixed' as const,
    borderLeft: `4px solid ${TOKENS.color.brand.royalBlue}`,
  } as React.CSSProperties,

  /**
   * Benefit/highlight block
   */
  benefitBlock: {
    backgroundColor: TOKENS.color.neutral.white,
    border: `1px solid ${TOKENS.color.neutral.smoke}`,
    borderRadius: TOKENS.radius.md,
    padding: TOKENS.spacing.scale[4],
    margin: `${TOKENS.spacing.scale[3]} 0`,
    /* Benefit block styling */
    boxShadow: `0 1px 3px rgba(0,0,0,0.1)`,
    position: 'relative',
  } as React.CSSProperties,

  /**
   * Detail information block
   */
  detailBlock: {
    backgroundColor: TOKENS.color.neutral.white,
    border: `2px solid ${TOKENS.color.brand.gold}`,
    borderRadius: TOKENS.radius.md,
    padding: TOKENS.spacing.scale[5],
    margin: `${TOKENS.spacing.scale[4]} 0`,
    /* Detail block with branding */
    position: 'relative',
    boxShadow: `0 2px 6px rgba(0,0,0,0.1)`,
  } as React.CSSProperties,
};

/**
 * Button variant utility function
 * @param variant - Button variant to get
 * @returns Button styles for the variant
 */
export const getButtonVariant = (variant: 'primary' | 'secondary' | 'outline'): React.CSSProperties => {
  const variants = {
    primary: componentStyles.buttonPrimary,
    secondary: componentStyles.buttonSecondary,
    outline: componentStyles.buttonOutline,
  };

  return variants[variant] || componentStyles.buttonPrimary;
};

/**
 * Component spacing utilities for consistent layouts
 */
export const componentSpacing = {
  buttonGroup: {
    display: 'inline-block',
    margin: '0 4px',
  },

  cardRow: {
    margin: `${TOKENS.spacing.scale[3]} 0`,
  },

  elementGroup: {
    margin: `${TOKENS.spacing.scale[4]} 0`,
  },
} as const;

/**
 * Email-safe interactive states (limited support)
 */
export const interactiveStates = {
  buttonHover: {
    opacity: '0.9',
    /* Fallback for email clients without hover support */
    transform: 'translateY(-1px)',
  } as React.CSSProperties,

  cardHover: {
    transform: 'translateY(-2px)',
    boxShadow: `0 4px 12px rgba(0,0,0,0.15)`,
  } as React.CSSProperties,
} as const;