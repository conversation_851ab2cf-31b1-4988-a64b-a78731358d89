import { TOKENS } from '../../lib/tokens';

/**
 * Email Base Styles Module
 *
 * Contains foundational styles, utility styles, accessibility features,
 * and responsive/mobile styles optimized for email client compatibility.
 *
 * @module email-base
 */

export interface EmailBaseStyles {
  main: React.CSSProperties;
  container: React.CSSProperties;
  responsiveContainer: React.CSSProperties;
  mobileContainer: React.CSSProperties;
  accessibility: {
    screenReaderOnly: React.CSSProperties;
    highContrastText: React.CSSProperties;
    focusVisible: React.CSSProperties;
  };
  utility: {
    center: React.CSSProperties;
    left: React.CSSProperties;
    right: React.CSSProperties;
    hidden: React.CSSProperties;
    block: React.CSSProperties;
    fullWidth: React.CSSProperties;
  };
  mobile: {
    responsiveText: React.CSSProperties;
    responsiveTable: React.CSSProperties;
    mobileStack: React.CSSProperties;
  };
}

/**
 * Base wrapper for email body with email-safe defaults
 */
export const baseStyles: EmailBaseStyles = {
  /**
   * Main email body container with responsive foundation
   */
  main: {
    backgroundColor: TOKENS.color.neutral.white,
    fontFamily: TOKENS.typography.family.primary,
    fontSize: TOKENS.typography.scale.body,
    color: TOKENS.color.neutral.nearBlack,
    lineHeight: '1.6',
    margin: '0',
    padding: '0',
    width: '100%',
    /* Webkit and MS font smoothing for better rendering */
    WebkitFontSmoothing: 'antialiased',
    MozOsxFontSmoothing: 'grayscale',
  } as React.CSSProperties,

  /**
   * Standard email container with email client compatibility
   */
  container: {
    margin: '0 auto',
    padding: `${TOKENS.spacing.scale[5]} 0 ${TOKENS.spacing.scale[12]}`,
    width: '560px',
    maxWidth: '100%',
    /* Ensure proper box model in all email clients */
    boxSizing: 'border-box' as const,
    /* Prevent autozoom on iOS */
    msTextSizeAdjust: '100%',
    webkitTextSizeAdjust: '100%',
  } as React.CSSProperties,

  /**
   * Responsive container for fluid layouts
   */
  responsiveContainer: {
    margin: '0 auto',
    padding: `${TOKENS.spacing.scale[4]} 20px ${TOKENS.spacing.scale[8]}`,
    width: '100%',
    maxWidth: '600px',
    boxSizing: 'border-box' as const,
  } as React.CSSProperties,

  /**
   * Mobile-specific container with adjusted spacing
   */
  mobileContainer: {
    margin: '0 auto',
    padding: `${TOKENS.spacing.scale[3]} 15px ${TOKENS.spacing.scale[6]}`,
    width: '100%',
    maxWidth: '100%',
    boxSizing: 'border-box' as const,
  } as React.CSSProperties,

  /**
   * Accessibility-oriented styles for inclusive design
   */
  accessibility: {
    /**
     * Screen reader only content
     */
    screenReaderOnly: {
      position: 'absolute' as const,
      left: '-10000px',
      top: 'auto',
      width: '1px',
      height: '1px',
      overflow: 'hidden',
    } as React.CSSProperties,

    /**
     * High contrast text for accessibility compliance
     */
    highContrastText: {
      color: TOKENS.color.neutral.nearBlack,
      fontWeight: TOKENS.typography.weights.bold,
      backgroundColor: 'transparent',
      border: 'none',
    } as React.CSSProperties,

    /**
     * Focus visible styles for keyboard navigation
     */
    focusVisible: {
      outline: `2px solid ${TOKENS.color.brand.royalBlue}`,
      outlineOffset: '2px',
    } as React.CSSProperties,
  },

  /**
   * Utility styles for common patterns
   */
  utility: {
    center: {
      textAlign: 'center' as const,
      margin: '0 auto',
    },

    left: {
      textAlign: 'left' as const,
    },

    right: {
      textAlign: 'right' as const,
    },

    hidden: {
      display: 'none',
    },

    block: {
      display: 'block',
    },

    fullWidth: {
      width: '100%',
      boxSizing: 'border-box' as const,
    },
  },

  /**
   * Mobile-specific responsive styles
   */
  mobile: {
    /**
     * Responsive text that scales appropriately
     */
    responsiveText: {
      fontSize: TOKENS.typography.scale.body,
      lineHeight: '1.5',
      /* Media query fallbacks for email clients */
      maxWidth: '100%',
      wordWrap: 'break-word' as const,
      overflowWrap: 'break-word' as const,
    } as React.CSSProperties,

    /**
     * Mobile-friendly table styles
     */
    responsiveTable: {
      width: '100%',
      tableLayout: 'fixed' as const,
      borderCollapse: 'collapse' as const,
      /* Mobile table handling */
      fontSize: '14px',
      lineHeight: '1.4',
    } as React.CSSProperties,

    /**
     * Mobile stacking utility for vertical layouts
     */
    mobileStack: {
      display: 'block',
      width: '100%',
      margin: `${TOKENS.spacing.scale[2]} 0`,
    } as React.CSSProperties,
  },
};

/**
 * Email-safe CSS media query fallbacks
 * Note: Most email clients don't support media queries, but these provide graceful degradation
 */
export const emailMediaFallbacks = `
  /* Outlook-specific fixes */
  @media screen and (-webkit-min-device-pixel-ratio:0) {
    .outlook-fix {
      font-family: Arial, sans-serif !important;
    }
  }

  /* iOS mail app specific styles */
  @media only screen and (max-width: 480px) {
    .mobile-text {
      font-size: 16px !important;
    }
  }
`;

/**
 * Utility function to merge base styles with component-specific styles
 * @param overrides - Style overrides to merge with base
 * @returns Merged style object
 */
export const mergeStyles = <T extends React.CSSProperties>(
  base: T,
  overrides: Partial<T>
): T => ({
  ...base,
  ...overrides,
});