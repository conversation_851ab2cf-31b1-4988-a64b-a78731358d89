/**
 * Email Styles Index Module
 *
 * Central export point for all email styling modules.
 * Provides a unified interface for email component styling.
 *
 * @module email-styles
 */

export * from './email-base';
export * from './email-typography';
export * from './email-layout';
export * from './email-components';
export * from './email-themes';

/**
 * Complete email styles collection
 * Combines all modular styles for easy importing
 */
export const emailStyles = {
  // Base styles
  ...require('./email-base').baseStyles,

  // Typography
  ...require('./email-typography').typographyStyles,

  // Layout
  ...require('./email-layout').layoutStyles,

  // Components
  ...require('./email-components').componentStyles,

  // Theme defaults
  themes: require('./email-themes').themeStyles,
};

// Legacy compatibility - maintain existing API
export { emailStyles as default };