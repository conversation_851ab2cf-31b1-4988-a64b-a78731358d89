import {
  Body,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Text,
} from '@react-email/components';

import { <PERSON><PERSON><PERSON>eader, EmailFooter, CTAButton, emailStyles } from './components';

interface WelcomeEmailProps {
  name?: string;
  clubName?: string;
  eventDate?: string;
  eventName?: string;
}

export const WelcomeEmail = ({
  name = '<PERSON><PERSON>',
  clubName = 'Tunis Doyen Rotary Club',
  eventDate = 'September 15, 2023',
  eventName = 'Monthly Meeting',
}: WelcomeEmailProps) => {
  const previewText = `Welcome to ${clubName}!`;

  return (
    <Html>
      <Head />
      <Preview>{previewText}</Preview>
      <Body style={emailStyles.main}>
        <Container style={emailStyles.container}>
          <EmailHeader />
          <Heading style={emailStyles.h1}>Welcome to {clubName}!</Heading>
          <Text style={emailStyles.text}>
            Hello {name},
          </Text>
          <Text style={emailStyles.text}>
            We're thrilled to have you join our community! {clubName} is dedicated to the Rotary motto "Service Above Self" - serving our community, fostering fellowship, and making a positive impact through humanitarian projects and professional networking.
          </Text>
          <Section style={emailStyles.center}>
            <CTAButton
              href="https://tunisdoyenrotary.org"
              variant="primary"
            >
              Visit Our Website
            </CTAButton>
          </Section>
          <Text style={emailStyles.text}>
            We have an upcoming event that we think you'll enjoy:
          </Text>
          <Section style={emailStyles.card}>
            <Text style={emailStyles.h3}>{eventName}</Text>
            <Text style={{ ...emailStyles.text, color: '#17458F', fontWeight: 'bold' }}>
              {eventDate}
            </Text>
            <Text style={emailStyles.smallText}>
              Join us for our monthly meeting where we discuss upcoming projects and community initiatives.
            </Text>
          </Section>
          <Text style={emailStyles.text}>
            If you have any questions, feel free to reach out to <NAME_EMAIL>.
          </Text>

          <Section style={emailStyles.section}>
            <Text style={emailStyles.text}>
              Ready to make a difference? Join us in our mission of service above self.
            </Text>
            <CTAButton
              href="https://tunisdoyenrotary.org/join"
              variant="gold"
            >
              Get Involved Today
            </CTAButton>
          </Section>

          <Text style={emailStyles.text}>
            Best regards,
            <br />
            The {clubName} Team
          </Text>

          <EmailFooter />
        </Container>
      </Body>
    </Html>
  );
};

export default WelcomeEmail;

// All styles are now handled by reusable components
