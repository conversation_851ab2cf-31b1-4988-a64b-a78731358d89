@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;600;700&display=swap');

:root {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  /* Rotary Brand Colors */
  --rotary-royal-blue: #17458F;
  --rotary-royal-blue-dark: #123F7A;
  --rotary-gold: #F7A81B;
  --rotary-white: #FFFFFF;
  --rotary-near-black: #111111;
  --rotary-charcoal: #54565A;
  --rotary-slate: #657F99;
  --rotary-smoke: #B1B1B1;
  --rotary-powder-blue: #B9D9EB;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Open Sans', Arial, system-ui, sans-serif;
  line-height: 1.6;
  color: var(--rotary-near-black);
  background-color: #f5f5f5;
}

#root {
  margin: auto;
}

/* Email-specific styles */
.email-container {
  max-width: 600px;
  margin: 0 auto;
  background-color: var(--rotary-white);
}

.email-section {
  padding: 24px;
}

.email-header {
  background-color: var(--rotary-royal-blue);
  color: var(--rotary-white);
  text-align: center;
  padding: 32px 24px;
}

.email-footer {
  background-color: var(--rotary-royal-blue);
  color: var(--rotary-white);
  text-align: center;
  padding: 32px 24px;
}

/* Responsive email styles */
@media only screen and (max-width: 600px) {
  .email-container {
    width: 100% !important;
  }
  
  .email-section {
    padding: 16px !important;
  }
  
  .email-header,
  .email-footer {
    padding: 20px 16px !important;
  }
}