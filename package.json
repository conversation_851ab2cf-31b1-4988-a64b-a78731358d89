{"name": "tunis-doyen-rotary-club-emails", "version": "1.0.0", "private": true, "scripts": {"build": "email build", "dev": "email dev", "export": "email export", "send-welcome": "node -r ts-node/register scripts/send-welcome.ts", "send-newsletter": "node -r ts-node/register scripts/send-newsletter.ts", "send-invitation": "node -r ts-node/register scripts/send-invitation.ts", "send-event-invitation-to-members": "node -r ts-node/register scripts/send-event-invitation-to-members.ts", "generate-contacts-csv": "node -r ts-node/register scripts/generate-contacts-csv.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "prepare": "husky"}, "dependencies": {"@react-email/components": "0.5.1", "dotenv": "^17.2.1", "globals": "^16.3.0", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^0.17.2"}, "devDependencies": {"@react-email/preview-server": "4.2.8", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@types/estree": "^1.0.6", "@types/jest": "^29.5.12", "@types/json-schema": "^7.0.15", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.1", "@typescript-eslint/eslint-plugin": "^8.42.0", "@typescript-eslint/parser": "^8.42.0", "eslint": "^9.34.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-beta.0", "husky": "^9.0.11", "i18next": "^25.5.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "prettier": "^3.2.5", "react-email": "4.2.8", "react-i18next": "^15.7.3", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.5.4"}, "overrides": {"axios": "^1.9.0", "next": "^15.4.7"}}