import React from 'react';
import RotaryEventInvitation from '../emails/rotary-event-invitation';

export default function RotaryEmailPreview() {
  return (
    <div style={{ 
      backgroundColor: '#f5f5f5', 
      minHeight: '100vh', 
      padding: '20px',
      fontFamily: 'Arial, sans-serif'
    }}>
      <div style={{ 
        maxWidth: '800px', 
        margin: '0 auto',
        backgroundColor: 'white',
        borderRadius: '8px',
        overflow: 'hidden',
        boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
      }}>
        <RotaryEventInvitation
          eventName="Inside the Webinar"
          eventDate="April 20th, 2024"
          eventTime="6:00 PM"
          eventLocation="Virtual Event"
          eventDescription="Unlock the potential of digital marketing and stay ahead of the curve with our insider knowledge and strategy showcase."
          heroImageUrl="https://via.placeholder.com/600x300/17458F/FFFFFF?text=Rotary+Foundation+Event"
          clubName="Rotary Foundation"
          contactEmail="<EMAIL>"
          websiteUrl="https://rotary.org"
          rsvpUrl="mailto:<EMAIL>?subject=RSVP for Inside the Webinar"
          calendarUrl="#"
        />
      </div>
      
      <div style={{
        maxWidth: '800px',
        margin: '20px auto 0',
        padding: '20px',
        backgroundColor: 'white',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <h2 style={{ color: '#17458F', marginBottom: '16px' }}>Email Template Preview</h2>
        <p style={{ color: '#666', lineHeight: 1.6 }}>
          This is a preview of the Rotary event invitation email template, designed to match 
          Rotary's brand guidelines and "People of Action" messaging. The template includes:
        </p>
        <ul style={{ color: '#666', lineHeight: 1.6 }}>
          <li>Professional header with Rotary branding</li>
          <li>Hero image section</li>
          <li>Structured event details card</li>
          <li>Clear call-to-action buttons</li>
          <li>Footer with Rotary messaging</li>
        </ul>
      </div>
    </div>
  );
}